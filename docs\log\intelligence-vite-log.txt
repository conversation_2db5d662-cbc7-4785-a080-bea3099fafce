[1] [CORE] ✅ Valid intelligence loaded with 6 ideas
[1] [API] intelligence:read - Duration: 2ms
[1] [API] intelligence:read - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started"]
[1] [SIMPLE-INTEL] 🧠 intelligence:read called: {
[1]   filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx',
[1]   vaultPath: 'C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started'
[1] }
[1] [SIMPLE-INTEL] 🔍 FileSystem instance: true
[1] [SIMPLE-INTEL] 🔍 FileSystem type: FileSystemManager
[1] [SIMPLE-INTEL] 🔍 Has getVaultRootPath: true
[1] [SIMPLE-INTEL] 🔍 Allowed roots: [ 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3' ]
[1] [SECURITY] ✅ Vault path inferred from documents token: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [SIMPLE-INTEL] 🔍 Inferred vault: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [CORE] 🔍 IntelligenceCoreService.get called
[1] [CORE] 🔍 filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
[1] [CORE] 🔍 vaultPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [CORE] 🔍 fileHash: 22年China SEO Report_February for Vendor.pptx_6zz2i8
[1] [CORE] 🔍 jsonPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\.intelligence\files\22年China SEO Report_February for Vendor.pptx_6zz2i8.json
[1] [CORE] ✅ Valid intelligence loaded with 6 ideas
[1] [API] intelligence:read - Duration: 3ms
[1] [API] intelligence:read - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started"]
[1] [SIMPLE-INTEL] 🧠 intelligence:read called: {
[1]   filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx',
[1]   vaultPath: 'C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started'
[1] }
[1] [SIMPLE-INTEL] 🔍 FileSystem instance: true
[1] [SIMPLE-INTEL] 🔍 FileSystem type: FileSystemManager
[1] [SIMPLE-INTEL] 🔍 Has getVaultRootPath: true
[1] [SIMPLE-INTEL] 🔍 Allowed roots: [ 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3' ]
[1] [SECURITY] ✅ Vault path inferred from documents token: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [SIMPLE-INTEL] 🔍 Inferred vault: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [CORE] 🔍 IntelligenceCoreService.get called
[1] [CORE] 🔍 filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
[1] [CORE] 🔍 vaultPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [CORE] 🔍 fileHash: 22年China SEO Report_February for Vendor.pptx_6zz2i8
[1] [CORE] 🔍 jsonPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\.intelligence\files\22年China SEO Report_February for Vendor.pptx_6zz2i8.json
[1] [CORE] ✅ Valid intelligence loaded with 6 ideas
[1] [API] intelligence:read - Duration: 5ms
[1] [API] intelligence:write - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started",{"json":{"file_path":"C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","key_ideas":[{"id":"idea_1","text":"Baidu SEO Performance  Ranking","relevance_score":95,"intent_types":["topic","metric"],"weight":1
[1] [SIMPLE-INTEL] 🧠 intelligence:write called: {
[1]   filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx',
[1]   vaultPath: 'C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started',
[1]   payloadType: 'object',
[1]   payloadKeys: [ 'json' ],
[1]   isContextNotes: false,
[1]   timestamp: '2025-08-27T09:05:54.308Z'
[1] }
[1] [SECURITY] 🚨 Absolute path detected in file_path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
[1] [SIMPLE-INTEL] 🚨 Intelligence data validation failed
[1] [SECURITY] ✅ Path sanitized and validated: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor
.pptx
[1] [API] intelligence:write - Duration: 1ms
[1] [API] intelligence:write - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started",{"json":{"file_path":"C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","key_ideas":[{"id":"idea_1","text":"Baidu SEO Performance  Ranking","relevance_score":95,"intent_types":["topic","metric"],"weight":1
[1] [SECURITY] 🚨 Absolute path detected in file_path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
[1] [SIMPLE-INTEL] 🚨 Intelligence data validation failed
[1] [SIMPLE-INTEL] 🧠 intelligence:write called: {
[1]   filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx',
[1]   vaultPath: 'C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started',
[1]   payloadType: 'object',
[1]   payloadKeys: [ 'json' ],
[1]   isContextNotes: false,
[1]   timestamp: '2025-08-27T09:05:55.312Z'
[1] }
[1] [SECURITY] ✅ Path sanitized and validated: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor
.pptx
[1] [API] intelligence:write - Duration: 1ms
[1] [API] intelligence:write - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started",{"json":{"file_path":"C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","key_ideas":[{"id":"idea_1","text":"Baidu SEO Performance  Ranking","relevance_score":95,"intent_types":["topic","metric"],"weight":1
[1] [SECURITY] 🚨 Absolute path detected in file_path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
[1] [SIMPLE-INTEL] 🚨 Intelligence data validation failed
[1] [SIMPLE-INTEL] 🧠 intelligence:write called: {
[1]   filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx',
[1]   vaultPath: 'C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started',
[1]   payloadType: 'object',
[1]   payloadKeys: [ 'json' ],
[1]   isContextNotes: false,
[1]   timestamp: '2025-08-27T09:05:57.320Z'
[1] }
[1] [SECURITY] ✅ Path sanitized and validated: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor
.pptx
[1] [API] intelligence:write - Duration: 0ms
[1] [API] intelligence:write - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started",{"json":{"file_path":"C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","key_ideas":[{"id":"idea_1","text":"Baidu SEO Performance  Ranking","relevance_score":95,"intent_types":["topic","metric"],"weight":1
[1] [SECURITY] 🚨 Absolute path detected in file_path: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
[1] [SIMPLE-INTEL] 🚨 Intelligence data validation failed
[1] [SIMPLE-INTEL] 🧠 intelligence:write called: {
[1]   filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx',
[1]   vaultPath: 'C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started',
[1]   payloadType: 'object',
[1]   payloadKeys: [ 'json' ],
[1]   isContextNotes: false,
[1]   timestamp: '2025-08-27T09:06:00.322Z'
[1] }
[1] [SECURITY] ✅ Path sanitized and validated: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor
.pptx
[1] [API] intelligence:write - Duration: 0ms
[1] [API] intelligence:read - Request: ["C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx","C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started"]
[1] [SIMPLE-INTEL] 🧠 intelligence:read called: {
[1]   filePath: 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx',
[1]   vaultPath: 'C:/Users/<USER>/Documents/Post-Kernel-Test3/personal-vault/getting-started'
[1] }
[1] [SIMPLE-INTEL] 🔍 FileSystem instance: true
[1] [SIMPLE-INTEL] 🔍 FileSystem type: FileSystemManager
[1] [SIMPLE-INTEL] 🔍 Has getVaultRootPath: true
[1] [SIMPLE-INTEL] 🔍 Allowed roots: [ 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3' ]
[1] [SECURITY] ✅ Vault path inferred from documents token: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [SIMPLE-INTEL] 🔍 Inferred vault: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [CORE] 🔍 IntelligenceCoreService.get called
[1] [CORE] 🔍 filePath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\documents\22年China SEO Report_February for Vendor.pptx
[1] [CORE] 🔍 vaultPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started
[1] [CORE] 🔍 fileHash: 22年China SEO Report_February for Vendor.pptx_6zz2i8
[1] [CORE] 🔍 jsonPath: C:\Users\<USER>\Documents\Post-Kernel-Test3\personal-vault\getting-started\.intelligence\files\22年China SEO Report_February for Vendor.pptx_6zz2i8.json
[1] [CORE] ✅ Valid intelligence loaded with 6 ideas
[1] [API] intelligence:read - Duration: 2ms
