/**
 * Simple Intelligence Module
 * Direct replacement for the current intelligence implementation
 * No external dependencies - works with existing system
 */

import { BaseAPIModule, ModuleConfig, ModuleDependency } from '../core/BaseAPIModule'

export class SimpleIntelligenceModule extends BaseAPIModule {
  readonly name = 'intelligence'
  readonly version = '1.0.0'
  readonly description = 'Simple intelligence operations - direct replacement for current implementation'
  readonly dependencies: ModuleDependency[] = []

  private intelCore: any
  private fileSystem: any
  private PathResolver: any
  private database: any

  protected async onInitialize(): Promise<void> {
    try {
      console.log('[SIMPLE-INTEL] 🏗️ SimpleIntelligenceModule.onInitialize() called')

      // Import required modules dynamically
      console.log('[SIMPLE-INTEL] 🔍 Importing required modules...')
      const { IntelligenceCoreService } = await import('../../../core/IntelligenceCoreService')
      const { FileSystemManager } = await import('../../../fileSystem')
      const { PathResolver } = await import('../../../core/PathResolver')
      const { DatabaseManager } = await import('../../../database')
      console.log('[SIMPLE-INTEL] 🔍 All modules imported successfully')

      // Use shared DatabaseManager instance if available, otherwise create new one
      let dbManager: any
      if ((global as any).sharedDatabaseManager) {
        console.log('[SIMPLE-INTEL] 🔍 Using shared DatabaseManager instance')
        dbManager = (global as any).sharedDatabaseManager
      } else {
        console.log('[SIMPLE-INTEL] 🔍 Creating new DatabaseManager instance')
        dbManager = new DatabaseManager()
        // Store as shared instance for other modules
        ;(global as any).sharedDatabaseManager = dbManager
      }

      this.intelCore = new IntelligenceCoreService()
      this.fileSystem = new FileSystemManager(dbManager)
      this.PathResolver = PathResolver
      this.database = dbManager

      // Debug: Verify FileSystemManager has the required method
      console.log('[SIMPLE-INTEL] 🔍 FileSystemManager methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(this.fileSystem)))
      console.log('[SIMPLE-INTEL] 🔍 Has getVaultRootPath method:', typeof this.fileSystem.getVaultRootPath === 'function')

      if (typeof this.fileSystem.getVaultRootPath === 'function') {
        try {
          const vaultRoot = this.fileSystem.getVaultRootPath()
          console.log('[SIMPLE-INTEL] 🔍 Vault root path:', vaultRoot)
        } catch (error) {
          console.error('[SIMPLE-INTEL] 🚨 Error calling getVaultRootPath:', error)
        }
      }

      console.log('[SIMPLE-INTEL] ✅ Simple Intelligence Module initialized successfully')
    } catch (error) {
      console.error('[SIMPLE-INTEL] ❌ Error in onInitialize():', error)
      throw error
    }
  }

  // FLOW1 FIX: Get dynamic vault root from settings instead of hardcoded FileSystemManager
  private async getDynamicVaultRoot(): Promise<string> {
    try {
      // Get vault root from database settings (the actual source of truth)
      const vaultRootSetting = await this.database.getSetting('vault-root-path')
      if (vaultRootSetting && typeof vaultRootSetting === 'string') {
        console.log('[Flow1] 🔧 Using dynamic vault root from settings:', vaultRootSetting)
        return vaultRootSetting
      }

      // Fallback to FileSystemManager if no setting found
      const fallbackRoot = this.fileSystem.getVaultRootPath()
      console.log('[Flow1] ⚠️ No vault-root-path setting found, using FileSystemManager fallback:', fallbackRoot)
      return fallbackRoot
    } catch (error: any) {
      console.error('[Flow1] 🚨 Error getting dynamic vault root:', error.message)
      // Final fallback to FileSystemManager
      return this.fileSystem.getVaultRootPath()
    }
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering simple intelligence endpoints...')

    // Main intelligence:write endpoint (the big one!)
    this.registerEndpoint('intelligence', 'write',
      async (filePath: string, vaultPath: string, payload: any) => {
        try {
          // [IPC-LOG] Log intelligence write calls
          console.log('[Flow1] 🧠 SimpleIntelligenceModule.intelligence:write called:', {
            filePath,
            vaultPath,
            payloadType: typeof payload,
            payloadKeys: payload ? Object.keys(payload) : [],
            isContextNotes: filePath.startsWith('context-notes/'),
            timestamp: new Date().toISOString()
          });

          // FLOW1 FIX: Get dynamic vault roots instead of hardcoded ones
          const dynamicVaultRoot = await this.getDynamicVaultRoot()
          const allowedRoots = [dynamicVaultRoot].filter(Boolean)

          console.log('[Flow1] 🔧 VAULT ROOT FIX - intelligence:write using dynamic vault root:', {
            hardcodedRoot: this.fileSystem.getVaultRootPath(),
            dynamicRoot: dynamicVaultRoot,
            filePath: filePath.substring(0, 100) + '...',
            vaultPath: vaultPath
          })
          
          // SECURITY CHECK: Prevent codebase contamination
          if (this.PathResolver.isCodebasePath(filePath)) {
            console.error('[SIMPLE-INTEL] 🚨 Rejecting codebase path in intelligence:write:', filePath)
            return { success: false, error: 'Security violation: Cannot write to codebase paths' }
          }
          
          // SECURITY CHECK: Validate vault path boundaries
          if (vaultPath && !this.PathResolver.validateVaultPath(vaultPath, allowedRoots)) {
            console.error('[SIMPLE-INTEL] 🚨 Vault path outside allowed boundaries:', vaultPath)
            console.error('[SIMPLE-INTEL] 🚨 This might be a context ID instead of a full vault path')
            console.error('[SIMPLE-INTEL] 🚨 Expected format: C:\\Users\\<USER>\\vault-name\\context-name')
            console.error('[SIMPLE-INTEL] 🚨 Received: ', vaultPath)
            return { success: false, error: `Security violation: Invalid vault path format. Expected full vault path, got: ${vaultPath}` }
          }
          
          // SECURITY ENHANCEMENT: Comprehensive path sanitization and validation
          const sanitizedFilePath = this.PathResolver.sanitizeAndValidatePath(filePath, allowedRoots)
          if (!sanitizedFilePath) {
            console.error('[SIMPLE-INTEL] 🚨 Path sanitization failed for:', filePath)
            return { success: false, error: 'Security violation: Invalid or malicious file path' }
          }
          
          // SECURITY ENHANCEMENT: Validate intelligence data before storage
          if (payload && !this.PathResolver.validateIntelligenceData(payload)) {
            console.error('[SIMPLE-INTEL] 🚨 Intelligence data validation failed')
            return { success: false, error: 'Security violation: Invalid intelligence data format' }
          }
          
          // SECURITY ENHANCEMENT: Validate context ID if provided
          if (payload?.storage_metadata?.context_path && 
              !this.PathResolver.validateContextId(payload.storage_metadata.context_path)) {
            console.error('[SIMPLE-INTEL] 🚨 Invalid context path in storage metadata:', payload.storage_metadata.context_path)
            return { success: false, error: 'Security violation: Invalid context identifier' }
          }
          
          const inferredVault = this.PathResolver.inferVaultPath(sanitizedFilePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          
          // SECURITY CHECK: Validate inferred vault path
          if (inferredVault && !this.PathResolver.validateVaultPath(inferredVault, allowedRoots)) {
            console.error('[SIMPLE-INTEL] 🚨 Inferred vault path outside allowed boundaries:', inferredVault)
            console.error('[SIMPLE-INTEL] 🚨 This might be a context ID instead of a full vault path')
            console.error('[SIMPLE-INTEL] 🚨 Expected format: C:\\Users\\<USER>\\vault-name\\context-name')
            console.error('[SIMPLE-INTEL] 🚨 Received: ', inferredVault)
            return { success: false, error: `Security violation: Inferred vault path outside allowed boundaries. Expected full vault path, got: ${inferredVault}` }
          }
          
          console.log('[Flow1] 🧠 Vault path resolution:', {
            originalVaultPath: vaultPath,
            sanitizedFilePath,
            inferredVault,
            allowedRoots,
            vaultRootPath: this.fileSystem.getVaultRootPath()
          });

          if (!inferredVault) {
            console.error('[Flow1] 🚨 Could not infer valid vault path for:', sanitizedFilePath)
            console.error('[Flow1] 🚨 This is likely the root cause of Flow1 failure!')
            return { success: false, error: 'Security violation: Unable to infer valid vault path' }
          }
          
          // SECURITY ENHANCEMENT: Use sanitized file path for storage
          const result = await this.intelCore.save(sanitizedFilePath, inferredVault, payload)
          
          console.log('[SIMPLE-INTEL] 🧠 intelligence:write result:', {
            filePath,
            success: result.success,
            error: result.error,
            timestamp: new Date().toISOString()
          });
          
          return result
        } catch (error: any) { 
          console.log('[SIMPLE-INTEL] ❌ intelligence:write error:', {
            filePath,
            vaultPath,
            error: error.message,
            timestamp: new Date().toISOString()
          });
          return { success: false, error: error.message } 
        }
      },
      {
        validator: (filePath: string, vaultPath: string, _payload: any) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Write intelligence data (canonical JSON, optional markdown) to context storage (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:read endpoint
    this.registerEndpoint('intelligence', 'read',
      async (filePath: string, vaultPath: string) => {
        try {
          console.log('[SIMPLE-INTEL] 🧠 intelligence:read called:', { filePath, vaultPath })
          console.log('[SIMPLE-INTEL] 🔍 FileSystem instance:', !!this.fileSystem)
          console.log('[SIMPLE-INTEL] 🔍 FileSystem type:', this.fileSystem?.constructor?.name)
          console.log('[SIMPLE-INTEL] 🔍 Has getVaultRootPath:', typeof this.fileSystem?.getVaultRootPath === 'function')

          if (!this.fileSystem) {
            throw new Error('FileSystem not initialized')
          }

          if (typeof this.fileSystem.getVaultRootPath !== 'function') {
            throw new Error(`FileSystem.getVaultRootPath is not a function. Available methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(this.fileSystem)).join(', ')}`)
          }

          // FLOW1 FIX: Use dynamic vault root
          const dynamicVaultRoot = await this.getDynamicVaultRoot()
          const allowedRoots = [dynamicVaultRoot].filter(Boolean)
          console.log('[Flow1] 🔧 intelligence:read using dynamic vault root:', dynamicVaultRoot)
          console.log('[SIMPLE-INTEL] 🔍 Allowed roots:', allowedRoots)

          const inferredVault = this.PathResolver.inferVaultPath(filePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          console.log('[SIMPLE-INTEL] 🔍 Inferred vault:', inferredVault)

          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.get(filePath, inferredVault)
        } catch (error: any) {
          console.error('[SIMPLE-INTEL] 🚨 intelligence:read error:', error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, vaultPath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Read intelligence data; if only markdown exists, stub JSON is created (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:save endpoint
    this.registerEndpoint('intelligence', 'save',
      async (filePath: string, vaultPath: string, payload: any) => {
        try {
          // FLOW1 FIX: Use dynamic vault root
          const dynamicVaultRoot = await this.getDynamicVaultRoot()
          const allowedRoots = [dynamicVaultRoot].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath(filePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.save(filePath, inferredVault, payload)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (filePath: string, vaultPath: string, _payload: any) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Save intelligence data with dual storage (.json canonical + .md optional) (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:get endpoint
    this.registerEndpoint('intelligence', 'get',
      async (filePath: string, vaultPath: string) => {
        try {
          // FLOW1 FIX: Use dynamic vault root
          const dynamicVaultRoot = await this.getDynamicVaultRoot()
          const allowedRoots = [dynamicVaultRoot].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath(filePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.get(filePath, inferredVault)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (filePath: string, vaultPath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Get intelligence data (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:analyze endpoint
    this.registerEndpoint('intelligence', 'analyze',
      async (filePath: string, vaultPath: string, options: any) => {
        try {
          // FLOW1 FIX: Use dynamic vault root
          const dynamicVaultRoot = await this.getDynamicVaultRoot()
          const allowedRoots = [dynamicVaultRoot].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath(filePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.analyze(filePath, inferredVault, options)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (filePath: string, vaultPath: string, options: any) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Analyze file content and generate intelligence (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:listSessions endpoint
    this.registerEndpoint('intelligence', 'listSessions',
      async (vaultPath: string) => {
        try {
          // FLOW1 FIX: Use dynamic vault root
          const dynamicVaultRoot = await this.getDynamicVaultRoot()
          const allowedRoots = [dynamicVaultRoot].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath('', { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.listSessions(inferredVault)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (vaultPath: string) => {
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'List intelligence sessions for a vault (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:writeSession endpoint
    this.registerEndpoint('intelligence', 'writeSession',
      async (sessionId: string, vaultPath: string, payload: any) => {
        try {
          // FLOW1 FIX: Use dynamic vault root
          const dynamicVaultRoot = await this.getDynamicVaultRoot()
          const allowedRoots = [dynamicVaultRoot].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath('', { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.writeSession(sessionId, inferredVault, payload)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (sessionId: string, vaultPath: string, payload: any) => {
          if (!this.validateInput(sessionId, 'string', 100)) throw new Error('Invalid session ID')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
          if (!payload || typeof payload !== 'object') throw new Error('Invalid payload')
        },
        description: 'Write session data to intelligence storage (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    this.log('info', `Registered ${this.endpoints.size} simple intelligence endpoints`)
  }
}
