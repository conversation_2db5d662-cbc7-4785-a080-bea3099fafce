FilesPage.tsx:175 [FILES] 🚨 Vault loading issues detected: ['3 operations failed']
initializeFilesPage @ FilesPage.tsx:175
await in initializeFilesPage
(anonymous) @ FilesPage.tsx:192
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17486
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
commitHookEffectListMount @ react-dom_client.js?v=58b462a4:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=58b462a4:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9899
flushPassiveEffects @ react-dom_client.js?v=58b462a4:11302
(anonymous) @ react-dom_client.js?v=58b462a4:11060
performWorkUntilDeadline @ react-dom_client.js?v=58b462a4:36
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=58b462a4:250
App @ App.tsx:214
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17424
renderWithHooks @ react-dom_client.js?v=58b462a4:4206
updateFunctionComponent @ react-dom_client.js?v=58b462a4:6619
beginWork @ react-dom_client.js?v=58b462a4:7654
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
performUnitOfWork @ react-dom_client.js?v=58b462a4:10868
workLoopSync @ react-dom_client.js?v=58b462a4:10728
renderRootSync @ react-dom_client.js?v=58b462a4:10711
performWorkOnRoot @ react-dom_client.js?v=58b462a4:10330
performSyncWorkOnRoot @ react-dom_client.js?v=58b462a4:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=58b462a4:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=58b462a4:11558
(anonymous) @ react-dom_client.js?v=58b462a4:11649
unifiedAnnotationService.ts:216 [UNIFIED-ANNOTATIONS] ❌ Failed to save annotations: APIError: Security violation: Invalid intelligence data format
    at UnifiedAPIClient.call (UnifiedAPIClient.ts:108:19)
    at async UnifiedAnnotationService.saveAnnotations (unifiedAnnotationService.ts:202:26)
    at async UnifiedAnnotationService.addAnnotation (unifiedAnnotationService.ts:242:14)
    at async handleUserInput (IntelligenceHub.tsx:328:27)
saveAnnotations @ unifiedAnnotationService.ts:216
await in saveAnnotations
addAnnotation @ unifiedAnnotationService.ts:242
await in addAnnotation
handleUserInput @ IntelligenceHub.tsx:328
executeDispatch @ react-dom_client.js?v=58b462a4:11736
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
processDispatchQueue @ react-dom_client.js?v=58b462a4:11772
(anonymous) @ react-dom_client.js?v=58b462a4:12182
batchedUpdates$1 @ react-dom_client.js?v=58b462a4:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=58b462a4:11877
dispatchEvent @ react-dom_client.js?v=58b462a4:14792
dispatchDiscreteEvent @ react-dom_client.js?v=58b462a4:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=58b462a4:250
IntelligenceHub @ IntelligenceHub.tsx:1294
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17424
renderWithHooks @ react-dom_client.js?v=58b462a4:4206
updateFunctionComponent @ react-dom_client.js?v=58b462a4:6619
beginWork @ react-dom_client.js?v=58b462a4:7654
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
performUnitOfWork @ react-dom_client.js?v=58b462a4:10868
workLoopSync @ react-dom_client.js?v=58b462a4:10728
renderRootSync @ react-dom_client.js?v=58b462a4:10711
performWorkOnRoot @ react-dom_client.js?v=58b462a4:10330
performSyncWorkOnRoot @ react-dom_client.js?v=58b462a4:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=58b462a4:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=58b462a4:11558
(anonymous) @ react-dom_client.js?v=58b462a4:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=58b462a4:250
FilePageOverlay @ FilePageOverlay.tsx:673
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17424
renderWithHooks @ react-dom_client.js?v=58b462a4:4206
updateFunctionComponent @ react-dom_client.js?v=58b462a4:6619
beginWork @ react-dom_client.js?v=58b462a4:7654
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
performUnitOfWork @ react-dom_client.js?v=58b462a4:10868
workLoopSync @ react-dom_client.js?v=58b462a4:10728
renderRootSync @ react-dom_client.js?v=58b462a4:10711
performWorkOnRoot @ react-dom_client.js?v=58b462a4:10330
performSyncWorkOnRoot @ react-dom_client.js?v=58b462a4:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=58b462a4:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=58b462a4:11558
(anonymous) @ react-dom_client.js?v=58b462a4:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=58b462a4:250
FileOverlayManager @ App.tsx:67
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17424
renderWithHooks @ react-dom_client.js?v=58b462a4:4206
updateFunctionComponent @ react-dom_client.js?v=58b462a4:6619
beginWork @ react-dom_client.js?v=58b462a4:7654
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
performUnitOfWork @ react-dom_client.js?v=58b462a4:10868
workLoopSync @ react-dom_client.js?v=58b462a4:10728
renderRootSync @ react-dom_client.js?v=58b462a4:10711
performWorkOnRoot @ react-dom_client.js?v=58b462a4:10330
performSyncWorkOnRoot @ react-dom_client.js?v=58b462a4:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=58b462a4:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=58b462a4:11558
(anonymous) @ react-dom_client.js?v=58b462a4:11649
IntelligenceHub.tsx:364 [ANNOTATIONS] 💾 ❌ Save-first failed: Error: Failed to save annotation to storage
    at handleUserInput (IntelligenceHub.tsx:331:15)
handleUserInput @ IntelligenceHub.tsx:364
await in handleUserInput
executeDispatch @ react-dom_client.js?v=58b462a4:11736
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
processDispatchQueue @ react-dom_client.js?v=58b462a4:11772
(anonymous) @ react-dom_client.js?v=58b462a4:12182
batchedUpdates$1 @ react-dom_client.js?v=58b462a4:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=58b462a4:11877
dispatchEvent @ react-dom_client.js?v=58b462a4:14792
dispatchDiscreteEvent @ react-dom_client.js?v=58b462a4:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=58b462a4:250
IntelligenceHub @ IntelligenceHub.tsx:1294
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17424
renderWithHooks @ react-dom_client.js?v=58b462a4:4206
updateFunctionComponent @ react-dom_client.js?v=58b462a4:6619
beginWork @ react-dom_client.js?v=58b462a4:7654
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
performUnitOfWork @ react-dom_client.js?v=58b462a4:10868
workLoopSync @ react-dom_client.js?v=58b462a4:10728
renderRootSync @ react-dom_client.js?v=58b462a4:10711
performWorkOnRoot @ react-dom_client.js?v=58b462a4:10330
performSyncWorkOnRoot @ react-dom_client.js?v=58b462a4:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=58b462a4:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=58b462a4:11558
(anonymous) @ react-dom_client.js?v=58b462a4:11649
<IntelligenceHub>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=58b462a4:250
FilePageOverlay @ FilePageOverlay.tsx:673
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17424
renderWithHooks @ react-dom_client.js?v=58b462a4:4206
updateFunctionComponent @ react-dom_client.js?v=58b462a4:6619
beginWork @ react-dom_client.js?v=58b462a4:7654
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
performUnitOfWork @ react-dom_client.js?v=58b462a4:10868
workLoopSync @ react-dom_client.js?v=58b462a4:10728
renderRootSync @ react-dom_client.js?v=58b462a4:10711
performWorkOnRoot @ react-dom_client.js?v=58b462a4:10330
performSyncWorkOnRoot @ react-dom_client.js?v=58b462a4:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=58b462a4:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=58b462a4:11558
(anonymous) @ react-dom_client.js?v=58b462a4:11649
<FilePageOverlay>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=58b462a4:250
FileOverlayManager @ App.tsx:67
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17424
renderWithHooks @ react-dom_client.js?v=58b462a4:4206
updateFunctionComponent @ react-dom_client.js?v=58b462a4:6619
beginWork @ react-dom_client.js?v=58b462a4:7654
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
performUnitOfWork @ react-dom_client.js?v=58b462a4:10868
workLoopSync @ react-dom_client.js?v=58b462a4:10728
renderRootSync @ react-dom_client.js?v=58b462a4:10711
performWorkOnRoot @ react-dom_client.js?v=58b462a4:10330
performSyncWorkOnRoot @ react-dom_client.js?v=58b462a4:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=58b462a4:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=58b462a4:11558
(anonymous) @ react-dom_client.js?v=58b462a4:11649
