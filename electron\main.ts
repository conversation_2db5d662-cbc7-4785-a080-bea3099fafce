import { app, BrowserWindow, ipcMain, IpcMainInvokeEvent, dialog, screen } from 'electron'
import { autoUpdater } from 'electron-updater'
import path from 'path'
import { isDev } from './utils'
import { DatabaseManager } from './database'
import { FileSystemManager } from './fileSystem'
import { APIRegistry } from './api/APIRegistry'
import { ModuleRegistry } from './api/modules/core/ModuleRegistry'
import { UniversalPluginManager } from './plugins/PluginManager'
import { PluginCapability } from './plugins/types'
import { PathResolver } from './core/PathResolver'
// TODO: Removed FileProcessingDiagnostics import - module marked for removal

class App {
  public mainWindow: BrowserWindow | null = null
  private db: DatabaseManager
  private fileSystem: FileSystemManager
  private apiRegistry: APIRegistry
  private moduleRegistry: ModuleRegistry
  private pluginManager: UniversalPluginManager

  private isQuitting: boolean = false

  constructor() {
    this.db = new DatabaseManager()
    this.fileSystem = new FileSystemManager(this.db)

    // Store shared database manager for modules to use
    ;(global as any).sharedDatabaseManager = this.db

    // Initialize API registry with middleware
    this.apiRegistry = new APIRegistry({
      logging: {
        logRequests: true,
        logResponses: false,
        logErrors: true,
        logPerformance: true,
        maxLogLength: 500
      },
      rateLimiting: {
        maxRequests: 200,
        windowMs: 60000 // 1 minute
      },
      security: {
        allowedOrigins: isDev ? ['file://', 'http://localhost:5173'] : ['file://'],
        requireAuth: false
      },
      errorHandling: {
        logErrors: true,
        sanitizeErrors: false // Keep detailed errors for development
      }
    })

    // Initialize modular registry system (Phase 1 - Foundation)
    this.moduleRegistry = ModuleRegistry.getInstance(this.apiRegistry)
    console.log('[MODULAR-REGISTRY] 🚀 ModuleRegistry initialized successfully')

    // Initialize plugin manager
    this.pluginManager = new UniversalPluginManager(this.apiRegistry)

    // Add plugin directories
    this.pluginManager.addPluginDirectory(path.join(__dirname, 'plugins'))
    this.pluginManager.addPluginDirectory(path.join(app.getPath('userData'), 'plugins'))

  }

  private openPDFViewer(filePath: string): void {
    const normalizedPath = path.normalize(filePath)
    const fileUrl = `file:///${normalizedPath.replace(/\\/g, '/')}`

    console.log('[PDF] Opening PDF viewer for:', fileUrl)

    // Create a new window specifically for PDF viewing
    const pdfWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        webSecurity: false, // Allow loading local files
        nodeIntegration: false,
        contextIsolation: true,
      },
      title: `PDF Viewer - ${path.basename(filePath)}`,
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false, // Don't show until ready
    })

    // Load the PDF directly
    pdfWindow.loadURL(fileUrl)

    // Show window when ready
    pdfWindow.once('ready-to-show', () => {
      pdfWindow.show()
      console.log('[PDF] PDF viewer window shown')
    })

    // Handle window closed
    pdfWindow.on('closed', () => {
      console.log('[PDF] PDF viewer window closed')
    })
  }

  private createWindow(): void {
    // Following Electron best practices from official documentation
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Don't show until ready-to-show event
      frame: false, // Frameless window for custom chrome
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
      titleBarOverlay: false,
      backgroundColor: '#111827', // Match our app's dark theme (gray-900)
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
    })

    // Intercept native window close to show renderer confirmation
    this.mainWindow.on('close', (e) => {
      if (this.isQuitting) return
      e.preventDefault()
      try { this.mainWindow?.webContents.send('app:request-close') } catch {}
    })


    // Use ready-to-show event for graceful window display (Electron best practice)
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show()

        // Open DevTools in development after window is shown
        if (isDev) {
          this.mainWindow.webContents.openDevTools()
        }
      }
    })

    if (isDev) {
      this.mainWindow.loadURL('http://localhost:5173')
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
    }

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private validateSender(frame: any): boolean {
    // Validate the sender is from your app
    if (!frame || !frame.url) return false
    return frame.url.startsWith('file://') ||
           frame.url.startsWith('http://localhost:5173')
  }

  private validateInput(input: any, expectedType: string, maxLength?: number): boolean {
    try {
      console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT START ===')
      console.log('🔍 [VAULT-SETTING] Input:', input)
      console.log('🔍 [VAULT-SETTING] Expected type:', expectedType)
      console.log('🔍 [VAULT-SETTING] Max length:', maxLength)
      console.log('🔍 [VAULT-SETTING] Actual type:', typeof input)
      
      // Type validation
      if (typeof input !== expectedType) {
        console.error(`🔍 [VAULT-SETTING] ❌ Invalid input type. Expected ${expectedType}, got ${typeof input}`)
        console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (TYPE MISMATCH) ===')
        return false
      }

      // Length validation for strings
      if (expectedType === 'string' && maxLength && input.length > maxLength) {
        console.error(`🔍 [VAULT-SETTING] ❌ Input too long. Max length: ${maxLength}, got: ${input.length}`)
        console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (TOO LONG) ===')
        return false
      }

      // SECURITY: Path injection prevention for string inputs
      if (expectedType === 'string') {
        console.log('🔍 [VAULT-SETTING] Performing security validation for string input...')
        
        // ALWAYS block directory traversal attempts (essential security)
        const hasDirectoryTraversal = input.includes('..') || input.includes('\\\\')
        console.log('🔍 [VAULT-SETTING] Has directory traversal:', hasDirectoryTraversal)
        
        if (hasDirectoryTraversal) {
          console.error('🔍 [VAULT-SETTING] 🚨 Rejecting input with directory traversal:', input)
          console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (BLOCKED TRAVERSAL) ===')
          return false
        }

        // Get user's security level preference
        const securityLevel = this.getUserSecurityLevel()
        console.log('🔍 [VAULT-SETTING] User security level:', securityLevel)
        
        // If security is disabled, only check directory traversal
        if (securityLevel === 'disabled') {
          console.log('🔍 [VAULT-SETTING] ✅ Security disabled - only blocking directory traversal')
          console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (SECURITY DISABLED) ===')
          return true
        }

        // For relaxed mode, only check directory traversal (already done above)
        if (securityLevel === 'relaxed') {
          console.log('🔍 [VAULT-SETTING] ✅ Relaxed security - only blocking directory traversal')
          console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (RELAXED SECURITY) ===')
          return true
        }

        // For moderate and strict modes, apply additional checks
        if (securityLevel === 'moderate' || securityLevel === 'strict') {
          console.log('🔍 [VAULT-SETTING] Applying moderate/strict security checks...')
          
          // Check for Windows drive letters and absolute paths
          const hasWindowsDrive = input.match(/^[A-Z]:\\/) || input.match(/^\/[A-Z]:\//)
          console.log('🔍 [VAULT-SETTING] Has Windows drive pattern:', hasWindowsDrive)
          
          if (hasWindowsDrive) {
            // Check against user's custom vault patterns first
            const customPatterns = this.getUserVaultPatterns()
            const matchesCustomPattern = customPatterns.some(pattern => {
              const regex = new RegExp(pattern.replace(/\*/g, '.*'))
              return regex.test(input)
            })
            
            if (matchesCustomPattern) {
              console.log('🔍 [VAULT-SETTING] ✅ Input matches custom vault pattern:', input)
              console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (CUSTOM PATTERN) ===')
              return true
            }

            // For strict mode, block all Windows drive paths unless they match custom patterns
            if (securityLevel === 'strict') {
              console.error('🔍 [VAULT-SETTING] 🚨 Strict mode: Rejecting Windows drive path:', input)
              console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (STRICT BLOCKED) ===')
              return false
            }

            // For moderate mode, allow common legitimate paths and user directories
            const legitimatePaths = ['Documents', 'Desktop', 'Downloads', 'ChatLo_Vaults', 'Post-Kernel-Test', 'Users', 'augment-projects', 'chat-locally', 'chatlo', 'Test18', 'Test20']
            const hasLegitimatePath = legitimatePaths.some(path => input.includes(path))

            // Also allow any path under user's home directory (C:\Users\<USER>\...)
            const isUserPath = input.match(/^[A-Z]:\\Users\\[^\\]+\\/) !== null

            // ENHANCED: Allow any path that looks like a valid Windows absolute path for vault operations
            const isValidWindowsPath = input.match(/^[A-Z]:\\[^<>:"|?*]+$/) !== null

            if (hasLegitimatePath || isUserPath || isValidWindowsPath) {
              console.log('🔍 [VAULT-SETTING] ✅ Moderate mode: Input contains legitimate path, is user path, or valid Windows path:', input)
              console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (MODERATE ALLOWED) ===')
              return true
            }

            console.error('🔍 [VAULT-SETTING] 🚨 Moderate mode: Rejecting suspicious Windows path:', input)
            console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (MODERATE BLOCKED) ===')
            return false
          }
        }
      }

      console.log('🔍 [VAULT-SETTING] ✅ Input validation passed')
      console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (SUCCESS) ===')
      return true
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error during input validation:', error)
      console.log('🔍 [VAULT-SETTING] === VALIDATE INPUT END (ERROR) ===')
      return false
    }
  }

  /**
   * Helper method to detect base64 strings
   */
  private isBase64String(str: string): boolean {
    try {
      // Check if string looks like base64 and can be decoded
      return Buffer.from(str, 'base64').toString('base64') === str
    } catch {
      return false
    }
  }

  /**
   * Get user's security level preference from database
   */
  private getUserSecurityLevel(): 'disabled' | 'relaxed' | 'moderate' | 'strict' {
    try {
      const securityLevel = this.db.getSetting('securityLevel')
      if (securityLevel && ['disabled', 'relaxed', 'moderate', 'strict'].includes(securityLevel)) {
        return securityLevel as 'disabled' | 'relaxed' | 'moderate' | 'strict'
      }
      return 'moderate' // Default fallback
    } catch (error) {
      console.warn('🔍 [VAULT-SETTING] Could not read security level, using default:', error)
      return 'moderate'
    }
  }

  /**
   * Get user's custom vault patterns from database
   */
  private getUserVaultPatterns(): string[] {
    try {
      const patterns = this.db.getSetting('allowedVaultPatterns')
      if (patterns && typeof patterns === 'string') {
        // Split by newlines and filter out empty lines
        return patterns.split('\n').filter(pattern => pattern.trim().length > 0)
      }
      // Default patterns for backward compatibility
      return ['ChatLo_Vaults', 'Post-Kernel-Test']
    } catch (error) {
      console.warn('🔍 [VAULT-SETTING] Could not read custom vault patterns, using defaults:', error)
      return ['ChatLo_Vaults', 'Post-Kernel-Test']
    }
  }

  private async createContextStructure(contextPath: string, contextName: string): Promise<void> {
    const fs = await import('fs')
    const path = await import('path')

    // Create context directory
    await fs.promises.mkdir(contextPath, { recursive: true })

    // Create subdirectories
    await fs.promises.mkdir(path.join(contextPath, 'documents'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'images'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'artifacts'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, '.intelligence'), { recursive: true })

    // Create master.md
    const masterContent = `# ${contextName}

Welcome to your intelligent context vault! This is your master document that serves as the central hub for organizing and understanding your project.

## Overview
This context vault helps you organize files, conversations, and AI insights in one place.

## Quick Start
1. **Add files** to the \`documents/\` folder to get started
2. **Start a conversation** using the chat interface with this context selected
3. **Organize insights** and progress notes right here in this document

## How It Works
- 📁 **Documents folder**: Store your project files here
- 🖼️ **Images folder**: Add screenshots, diagrams, and visual assets
- 🎯 **Artifacts folder**: Save important outputs from AI conversations
      - 🧠 **AI Memory**: The \`.intelligence/\` folder contains AI memory optimized for Gemma models

## AI Insights
*This section will be automatically updated as you add files and have conversations*

## Project Progress
*Use this space to track your progress and key milestones*

---
*Last updated: ${new Date().toLocaleString()}*
*Files: 0 | Conversations: 0*`

    await fs.promises.writeFile(path.join(contextPath, 'master.md'), masterContent, 'utf8')

    // Create context metadata
    const metadata = {
      id: contextName.toLowerCase().replace(/\s+/g, '-'),
      name: contextName,
      created: new Date().toISOString(),
      description: `Context vault for ${contextName}`,
      contextType: 'getting-started'
    }

    await fs.promises.writeFile(
      path.join(contextPath, '.intelligence', 'metadata.json'),
      JSON.stringify(metadata, null, 2),
      'utf8'
    )

    // Create AI memory file
    const memoryContent = `# AI Memory for ${contextName}

## Context Understanding
This context vault was just created and is ready for your first project.

## Key Concepts
*AI will learn and document key concepts from your files and conversations*

## Relationships
*Connections between files, ideas, and conversations will be tracked here*

## Memory Chunks
*Optimized memory chunks for Gemma models will be stored here*

---
*This file is automatically managed by ChatLo's AI system*`

    // TODO: Write the AI memory file (this was removed from registerCoreAPIs)
    // await fs.promises.writeFile(path.join(contextPath, '.intelligence', 'ai-memory.md'), memoryContent, 'utf8')
  }



  private async setupIPC(): Promise<void> {
    console.log('[MAIN] Setting up IPC handlers...')

    // Register core API categories
    console.log('[MAIN] Registering core APIs...')
    this.registerCoreAPIs()

    // Initialize modular registry system (Phase 1 - Foundation)
    console.log('[MAIN] Initializing modular registry system...')
    await this.initializeModularSystem()
    console.log('[MAIN] Modular registry system initialized successfully')

    // Initialize API registry (sets up IPC handlers) AFTER modules are loaded
    console.log('[MAIN] Initializing API registry with registered endpoints...')
    this.apiRegistry.initialize()
    console.log('[MAIN] API registry initialized successfully')

    // All database operations are now handled through APIRegistry

    // All intelligence and message operations are now handled through APIRegistry

    // All settings operations are now handled through APIRegistry

    // All file system operations are now handled through APIRegistry







    // All vault operations are now handled through APIRegistry


  }

  private setupAutoUpdater(): void {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify()
    }

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:checking-for-update')
      }
    })

    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-available', info)
      }
    })

    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-not-available')
      }
    })

    autoUpdater.on('error', (err) => {
      console.error('Auto-updater error:', err)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:error', err.message)
      }
    })

    autoUpdater.on('download-progress', (progressObj) => {
      console.log(`Download progress: ${progressObj.percent}%`)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:download-progress', progressObj)
      }
    })

    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-downloaded', info)
      }
    })
  }

  private setupCustomProtocol(): void {
    const { protocol } = require('electron')
    const fs = require('fs')
    const path = require('path')
    const mime = require('mime-types')

    protocol.registerFileProtocol('chatlo-file', (request, callback) => {
      try {
        // Extract file path from URL
        let encodedPath = request.url.substr(13) // Remove 'chatlo-file://'

        // Remove any leading slashes that might be added by the URL parsing
        if (encodedPath.startsWith('/')) {
          encodedPath = encodedPath.substr(1)
        }
        let filePath

        console.log('[PROTOCOL] Requested URL:', request.url)
        console.log('[PROTOCOL] Encoded path:', encodedPath)

        // Try to detect if it's base64 or URL encoded
        if (encodedPath.includes('%')) {
          // URL encoded (old method) - decode and fix path
          filePath = decodeURIComponent(encodedPath)
          console.log('[PROTOCOL] Using URL decoding')
        } else {
          // Base64 encoded (new method)
          try {
            filePath = Buffer.from(encodedPath, 'base64').toString('utf8')
            console.log('[PROTOCOL] Using Base64 decoding')
          } catch (e) {
            // Fallback to URL decoding if base64 fails
            filePath = decodeURIComponent(encodedPath)
            console.log('[PROTOCOL] Base64 failed, using URL decoding fallback')
          }
        }

        // Fix Windows path separators and normalize
        filePath = path.normalize(filePath.replace(/\//g, path.sep))

        console.log('[PROTOCOL] Decoded path:', filePath)
        console.log('[PROTOCOL] File exists:', fs.existsSync(filePath))

        // Validate file exists and is accessible
        if (!fs.existsSync(filePath)) {
          console.error('[PROTOCOL] File not found:', filePath)
          callback({ error: -6 }) // FILE_NOT_FOUND
          return
        }

        // Get MIME type
        const mimeType = mime.lookup(filePath) || 'application/octet-stream'
        console.log('[PROTOCOL] Serving file:', filePath, 'as', mimeType)

        callback({
          path: filePath,
          headers: {
            'Content-Type': mimeType,
            'Access-Control-Allow-Origin': '*'
          }
        })
      } catch (error) {
        console.error('[PROTOCOL] Error serving file:', error)
        callback({ error: -2 }) // FAILED
      }
    })

    console.log('[ELECTRON] Custom file protocol registered')
  }

  // Register core API endpoints
  private registerCoreAPIs(): void {
    try {
          // ✅ ALL API ENDPOINTS EXTRACTED TO MODULAR SYSTEM
    // All endpoints now use the modular registry system

    // Register critical fallback endpoints to ensure basic functionality
    this.registerFallbackEndpoints()

    // Intelligence module initialization moved to initializeModularSystem()

    // Database and settings module initialization moved to initializeModularSystem()

    this.initializeFilesystemModule().catch(error => {
      console.error('[FILESYSTEM-MODULE] Failed to initialize filesystem module:', error)
    })

    this.initializePluginModule().catch(error => {
      console.error('[PLUGIN-MODULE] Failed to initialize plugin module:', error)
    })

    this.initializeSystemModule().catch(error => {
      console.error('[SYSTEM-MODULE] Failed to initialize system module:', error)
    })

    this.initializeEventsModule().catch(error => {
      console.error('[EVENTS-MODULE] Failed to initialize events module:', error)
    })

    this.initializeUpdaterModule().catch(error => {
      console.error('[UPDATER-MODULE] Failed to initialize updater module:', error)
    })
    } catch (error) {
      console.error('[MAIN] Error in registerCoreAPIs:', error)
      throw error
    }
  }

  /**
   * Register critical fallback endpoints to ensure basic functionality
   */
  private registerFallbackEndpoints(): void {
    console.log('[FALLBACK] Registering critical fallback endpoints...')

    // Critical fallback: vault:getVaultRegistry
    this.apiRegistry.registerEndpoint('vault', 'getVaultRegistry',
      async () => {
        try {
          console.log('🔍 [FALLBACK-VAULT] === GET VAULT REGISTRY START ===')

          // Get or create shared database instance
          if (!(global as any).sharedDatabaseManager) {
            const { DatabaseManager } = await import('./database')
            ;(global as any).sharedDatabaseManager = new DatabaseManager()
          }
          const db = (global as any).sharedDatabaseManager

          // Get vault root path from database
          let vaultRoot = db.getSetting('vault-root-path')
          console.log('🔍 [FALLBACK-VAULT] Database setting "vault-root-path":', vaultRoot)

          // Fall back to default if no saved path
          if (!vaultRoot) {
            const os = await import('os')
            const path = await import('path')
            vaultRoot = path.join(os.homedir(), 'Documents', 'ChatLo_Vaults')
            console.log('🔍 [FALLBACK-VAULT] ⚠️ No database setting found, using default vault root:', vaultRoot)
          }

          // Create basic vault registry structure
          const vaultRegistry = {
            vaultRoot,
            contexts: [],
            lastScan: new Date().toISOString()
          }

          console.log('🔍 [FALLBACK-VAULT] === GET VAULT REGISTRY END ===')
          // Return wrapped response format expected by frontend
          return {
            success: true,
            data: vaultRegistry
          }
        } catch (error: any) {
          console.error('🔍 [FALLBACK-VAULT] Error in getVaultRegistry:', error)
          // Return wrapped error response format expected by frontend
          return {
            success: false,
            error: error.message,
            data: {
              vaultRoot: null,
              contexts: [],
              lastScan: new Date().toISOString()
            }
          }
        }
      },
      { description: 'Get vault registry (fallback implementation)' }
    )

    console.log('[FALLBACK] Critical fallback endpoints registered')
  }

  // TODO: Removed diagnoseFileProcessing method - diagnostic module marked for removal

  // Initialize plugins with timeout and non-blocking approach
  private async initializePlugins(): Promise<void> {
    try {
      console.log('[PLUGIN] Starting plugin initialization...')

      // Add timeout to prevent hanging
      const pluginInitPromise = this.initializePluginsInternal()
      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error('Plugin initialization timeout')), 5000)
      })

      await Promise.race([pluginInitPromise, timeoutPromise])
      console.log('[PLUGIN] Plugin initialization completed')

    } catch (error) {
      console.warn('[PLUGIN] Plugin initialization failed or timed out:', error)
      // Continue startup even if plugins fail
    }
  }

  private async initializePluginsInternal(): Promise<void> {
    // Discover plugins
    const manifests = await this.pluginManager.discoverPlugins()
    console.log(`[PLUGIN] Discovered ${manifests.length} plugins`)

    if (manifests.length === 0) {
      console.log('[PLUGIN] No plugins found, skipping plugin loading')
      return
    }

    // Load core plugins first
    const corePlugins = manifests.filter(m => m.id.startsWith('core-'))
    for (const manifest of corePlugins) {
      try {
        console.log(`[PLUGIN] Loading core plugin: ${manifest.id}`)
        await this.pluginManager.loadPlugin(manifest)
      } catch (error) {
        console.error(`[PLUGIN] Failed to load core plugin ${manifest.id}:`, error)
      }
    }

    // Load optional plugins
    const optionalPlugins = manifests.filter(m => !m.id.startsWith('core-'))
    for (const manifest of optionalPlugins) {
      try {
        console.log(`[PLUGIN] Loading optional plugin: ${manifest.id}`)
        await this.pluginManager.loadPlugin(manifest)
      } catch (error) {
        console.warn(`[PLUGIN] Failed to load optional plugin ${manifest.id}:`, error)
      }
    }
  }

  public async init(): Promise<void> {
    await app.whenReady()

    // Check if vault system is configured - required for new architecture
    try {
      const vaultRootPath = this.db.getSetting('vault-root-path')
      console.log('[ELECTRON] Checking for vault system, vault root path:', vaultRootPath)

      if (vaultRootPath) {

    // Handle confirmed close from renderer
    ipcMain.on('app:confirm-close', async () => {
      try {
        const enabled = !!this.db.getSetting('portable-mode-enabled')
        if (enabled) {
          try { this.db.safeClose() } catch {}
        }
      } finally {
        this.isQuitting = true
        try { this.mainWindow?.destroy() } catch {}
        app.quit()
      }
    })

    ipcMain.on('app:cancel-close', () => {
      // No-op; renderer keeps running
    })

        console.log('[ELECTRON] Vault system detected and ready')
      } else {
        console.log('[ELECTRON] No vault system configured - user will need to set up context vault on first run')
      }
    } catch (error) {
      console.error('Error checking vault system:', error)
    }

    await this.setupIPC()
    this.setupAutoUpdater()
    this.setupCustomProtocol()
    this.createWindow()

    // Initialize plugins in background after window is created
    this.initializePlugins().catch(error => {
      console.warn('[PLUGIN] Background plugin initialization failed:', error)
    })

    // Initialize PluginFileProcessor for Office document processing
    ;(async () => {
      try {
        const { pluginFileProcessor } = await import('./fileProcessors/PluginFileProcessor')
        await pluginFileProcessor.initialize()
        console.log('[PROCESSING] PluginFileProcessor initialized successfully - Office plugins loaded')
      } catch (e) {
        console.error('[PROCESSING] Failed to initialize PluginFileProcessor:', (e as any)?.message)
      }
    })()

    // Apply persisted processing settings (OCR toggle) without blocking UI
    ;(async () => {
      try {
        const enabled = !!this.db.getSetting('processing.enableOCR')
        const { pluginFileProcessor } = await import('./fileProcessors/PluginFileProcessor')
        pluginFileProcessor.setPluginEnabled('OCRPlugin', enabled)
        console.log(`[PROCESSING] OCR plugin ${enabled ? 'enabled' : 'disabled'} from settings`)
      } catch (e) {
        console.warn('[PROCESSING] Failed to apply persisted OCR setting:', (e as any)?.message)
      }
    })()

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow()
      }
    })

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })
  }

  /**
   * Initialize the modular registry system (Phase 1 - Foundation)
   * This method sets up the modular system alongside the existing APIRegistry
   */
  private async initializeModularSystem(): Promise<void> {
    try {
      console.log('[MODULAR-REGISTRY] 🚀 Starting modular system initialization...')

      // Store module registry globally for dependency resolution
      ;(global as any).moduleRegistry = this.moduleRegistry

      // Initialize modules in dependency order (sequential, not parallel)
      console.log('[MODULAR-REGISTRY] 🔧 Initializing critical modules in dependency order...')

      // Phase 1: Core modules with no dependencies
      await this.initializeSystemModule()        // CRITICAL: System operations (no deps)
      await this.initializeDatabaseModule()      // CRITICAL: Database operations (no deps)
      await this.initializeSettingsModule()     // CRITICAL: Settings functionality (no deps)
      await this.initializeEventsModule()       // CRITICAL: Event handling (no deps)
      await this.initializeUpdaterModule()      // CRITICAL: Auto-updater (no deps)

      // Phase 2: Modules that depend on core modules
      await this.initializeFilesystemModule()   // CRITICAL: File system operations (no deps)
      await this.initializePluginModule()       // CRITICAL: Plugin management (no deps)

      console.log('[MODULAR-REGISTRY] 🔍 About to initialize intelligence module...')
      await this.initializeIntelligenceModule() // CRITICAL: Intelligence operations (no deps)
      console.log('[MODULAR-REGISTRY] 🔍 Intelligence module initialization completed')

      // Phase 3: Modules that depend on database/filesystem
      await this.initializeVaultModule()        // CRITICAL: Vault management (depends on database)

      console.log('[MODULAR-REGISTRY] ✅ All critical modules initialized successfully')
      console.log('[MODULAR-REGISTRY] 🎯 Phase 1 complete: Full modular system running alongside APIRegistry')

    } catch (error) {
      console.error('[MODULAR-REGISTRY] ❌ Failed to initialize modular system:', error)
      console.log('[MODULAR-REGISTRY] ⚠️ Continuing with existing APIRegistry system only')
      // Don't throw - allow the app to continue with the old system
    }
  }

  private async initializeVaultModule(): Promise<void> {
    try {
      console.log('[VAULT-MODULE] 🚀 Initializing SimpleVaultModule...')

      const { SimpleVaultModule } = await import('./api/modules/vault/SimpleVaultModule')
      const simpleVaultModule = new SimpleVaultModule()

      await simpleVaultModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })

      // Register with module registry for dependency resolution
      this.moduleRegistry.registerModule('vault', simpleVaultModule)

      console.log('[VAULT-MODULE] ✅ SimpleVaultModule initialized successfully')
    } catch (error) {
      console.error('[VAULT-MODULE] ❌ Failed to initialize SimpleVaultModule:', error)
      console.log('[VAULT-MODULE] ⚠️ Continuing with existing vault endpoints')
    }
  }

  private async initializeIntelligenceModule(): Promise<void> {
    try {
      console.log('[INTEL-MODULE] 🚀 Initializing SimpleIntelligenceModule...')

      console.log('[INTEL-MODULE] 🔍 Importing SimpleIntelligenceModule...')
      const { SimpleIntelligenceModule } = await import('./api/modules/intelligence/SimpleIntelligenceModule')
      console.log('[INTEL-MODULE] 🔍 SimpleIntelligenceModule imported successfully')

      console.log('[INTEL-MODULE] 🔍 Creating SimpleIntelligenceModule instance...')
      const simpleIntelligenceModule = new SimpleIntelligenceModule()
      console.log('[INTEL-MODULE] 🔍 SimpleIntelligenceModule instance created')

      console.log('[INTEL-MODULE] 🔍 Initializing SimpleIntelligenceModule...')
      await simpleIntelligenceModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })
      console.log('[INTEL-MODULE] 🔍 SimpleIntelligenceModule.initialize() completed')

      // Register with module registry for dependency resolution
      this.moduleRegistry.registerModule('intelligence', simpleIntelligenceModule)
      console.log('[INTEL-MODULE] 🔍 SimpleIntelligenceModule registered with module registry')

      console.log('[INTEL-MODULE] ✅ SimpleIntelligenceModule initialized successfully')
    } catch (error) {
      console.error('[INTEL-MODULE] ❌ Failed to initialize SimpleIntelligenceModule:', error)
      console.error('[INTEL-MODULE] ❌ Error stack:', (error as any)?.stack)
      console.log('[INTEL-MODULE] ⚠️ Continuing with existing intelligence endpoints')
    }
  }

  private async initializeDatabaseModule(): Promise<void> {
    try {
      console.log('[DB-MODULE] 🚀 Initializing SimpleDatabaseModule...')

      const { SimpleDatabaseModule } = await import('./api/modules/database/SimpleDatabaseModule')
      const simpleDatabaseModule = new SimpleDatabaseModule()

      await simpleDatabaseModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })

      // Register with module registry for dependency resolution
      this.moduleRegistry.registerModule('database', simpleDatabaseModule)

      console.log('[DB-MODULE] ✅ SimpleDatabaseModule initialized successfully')
    } catch (error) {
      console.error('[DB-MODULE] ❌ Failed to initialize SimpleDatabaseModule:', error)
      console.log('[DB-MODULE] ⚠️ Continuing with existing database endpoints')
    }
  }

  private async initializeSettingsModule(): Promise<void> {
    try {
      console.log('[SETTINGS-MODULE] 🚀 Initializing SimpleSettingsModule...')
      

      
      const { SimpleSettingsModule } = await import('./api/modules/core/SimpleSettingsModule')
      const simpleSettingsModule = new SimpleSettingsModule()
      
      await simpleSettingsModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })

      // Register with module registry for dependency resolution
      this.moduleRegistry.registerModule('settings', simpleSettingsModule)

      console.log('[SETTINGS-MODULE] ✅ SimpleSettingsModule initialized successfully')
    } catch (error) {
      console.error('[SETTINGS-MODULE] ❌ Failed to initialize SimpleSettingsModule:', error)
      console.log('[SETTINGS-MODULE] ⚠️ Continuing with existing settings endpoints')
    }
  }

  private async initializeFilesystemModule(): Promise<void> {
    try {
      console.log('[FILESYSTEM-MODULE] 🚀 Initializing SimpleFilesystemModule...')
      const { SimpleFilesystemModule } = await import('./api/modules/filesystem/SimpleFilesystemModule')
      const simpleFilesystemModule = new SimpleFilesystemModule()
      await simpleFilesystemModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })

      // Register with module registry for dependency resolution
      this.moduleRegistry.registerModule('filesystem', simpleFilesystemModule)

      console.log('[FILESYSTEM-MODULE] ✅ SimpleFilesystemModule initialized successfully')
    } catch (error) {
      console.error('[FILESYSTEM-MODULE] ❌ Failed to initialize SimpleFilesystemModule:', error)
      console.log('[FILESYSTEM-MODULE] ⚠️ Continuing with existing filesystem endpoints')
    }
  }

  private async initializePluginModule(): Promise<void> {
    try {
      console.log('[PLUGIN-MODULE] 🚀 Initializing SimplePluginModule...')
      const { SimplePluginModule } = await import('./api/modules/plugins/SimplePluginModule')
      const simplePluginModule = new SimplePluginModule()
      await simplePluginModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })
      console.log('[PLUGIN-MODULE] ✅ SimplePluginModule initialized successfully')
    } catch (error) {
      console.error('[PLUGIN-MODULE] ❌ Failed to initialize SimplePluginModule:', error)
      console.log('[PLUGIN-MODULE] ⚠️ Continuing with existing plugin endpoints')
    }
  }

  private async initializeSystemModule(): Promise<void> {
    try {
      console.log('[SYSTEM-MODULE] 🚀 Initializing SimpleSystemModule...')
      const { SimpleSystemModule } = await import('./api/modules/system/SimpleSystemModule')
      const simpleSystemModule = new SimpleSystemModule()
      await simpleSystemModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })
      console.log('[SYSTEM-MODULE] ✅ SimpleSystemModule initialized successfully')
    } catch (error) {
      console.error('[SYSTEM-MODULE] ❌ Failed to initialize SimpleSystemModule:', error)
      console.log('[SYSTEM-MODULE] ⚠️ Continuing with existing system endpoints')
    }
  }

  private async initializeEventsModule(): Promise<void> {
    try {
      console.log('[EVENTS-MODULE] 🚀 Initializing SimpleEventsModule...')
      const { SimpleEventsModule } = await import('./api/modules/events/SimpleEventsModule')
      const simpleEventsModule = new SimpleEventsModule()
      await simpleEventsModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })
      console.log('[EVENTS-MODULE] ✅ SimpleEventsModule initialized successfully')
    } catch (error) {
      console.error('[EVENTS-MODULE] ❌ Failed to initialize SimpleEventsModule:', error)
      console.log('[EVENTS-MODULE] ⚠️ Continuing with existing events endpoints')
    }
  }

  private async initializeUpdaterModule(): Promise<void> {
    try {
      console.log('[UPDATER-MODULE] 🚀 Initializing SimpleUpdaterModule...')
      const { SimpleUpdaterModule } = await import('./api/modules/updater/SimpleUpdaterModule')
      const simpleUpdaterModule = new SimpleUpdaterModule()
      await simpleUpdaterModule.initialize(this.apiRegistry, {
        enabled: true,
        lazy: false,
        priority: 1
      })
      console.log('[UPDATER-MODULE] ✅ SimpleUpdaterModule initialized successfully')
    } catch (error) {
      console.error('[UPDATER-MODULE] ❌ Failed to initialize SimpleUpdaterModule:', error)
      console.log('[UPDATER-MODULE] ⚠️ Continuing with existing updater endpoints')
    }
  }
}

const application = new App()
application.init().catch(console.error)

ipcMain.on('window-minimize', () => {
  if (application.mainWindow) application.mainWindow.minimize();
});
ipcMain.on('window-maximize', () => {
  if (application.mainWindow) {
    if (application.mainWindow.isMaximized()) {
      application.mainWindow.unmaximize();
    } else {
      // Clear any aspect ratio lock before maximizing for cross-platform stability
      try { application.mainWindow.setAspectRatio(0); } catch {}
      application.mainWindow.maximize();
    }
  }
});
ipcMain.on('window-close', () => {
  if (application.mainWindow) application.mainWindow.close();
});

function setWindowToAspectFit(win: BrowserWindow, aspectRatio: number, scale: number = 0.82) {
  const display = screen.getDisplayMatching(win.getBounds());
  const work = display.workArea;

  // Scale down to a fraction of the available work area to avoid oversizing (esp. 4K)
  const scaledMaxW = Math.max(200, Math.floor(work.width * scale));
  const scaledMaxH = Math.max(200, Math.floor(work.height * scale));

  // Try width-limited fit first
  let width = Math.min(scaledMaxW, Math.round(scaledMaxH * aspectRatio));
  let height = Math.round(width / aspectRatio);

  // If height exceeds, switch to height-limited fit
  if (height > scaledMaxH) {
    height = scaledMaxH;
    width = Math.round(height * aspectRatio);
  }

  // Respect window's declared minimums if any (fallback to sensible defaults)
  const minW = Math.max(300, (win as any).getMinimumSize ? win.getMinimumSize()[0] : 800);
  const minH = Math.max(200, (win as any).getMinimumSize ? win.getMinimumSize()[1] : 600);

  width = Math.max(minW, Math.min(width, scaledMaxW));
  height = Math.max(minH, Math.min(height, scaledMaxH));

  // Final clamp ensuring aspect after min enforcement
  const widthFromHeight = Math.min(scaledMaxW, Math.max(minW, Math.round(height * aspectRatio)));
  const heightFromWidth = Math.min(scaledMaxH, Math.max(minH, Math.round(width / aspectRatio)));

  // Prefer the size that stays within bounds
  if (widthFromHeight <= scaledMaxW) {
    width = widthFromHeight;
    height = Math.round(width / aspectRatio);
  } else {
    height = heightFromWidth;
    width = Math.round(height * aspectRatio);
  }

  // Center within the target display work area to keep UX stable
  const x = work.x + Math.round((work.width - width) / 2);
  const y = work.y + Math.round((work.height - height) / 2);
  win.setBounds({ x, y, width, height }, false);
}

// Handle aspect ratio adjustments from renderer controls
ipcMain.on('window-aspect-16-9', () => {
  const win = application.mainWindow;
  if (!win) return;

  // Enforce 16:9 and snap size once for immediate feedback
  try { win.setAspectRatio(16 / 9); } catch {}
  setWindowToAspectFit(win, 16 / 9);
});

ipcMain.on('window-aspect-10-16', () => {
  const win = application.mainWindow;
  if (!win) return;

  // Enforce 10:16 and snap size once for immediate feedback
  try { win.setAspectRatio(10 / 16); } catch {}
  setWindowToAspectFit(win, 10 / 16);
});
