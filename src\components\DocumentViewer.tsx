import React, { useState, useEffect, useRef, useCallback } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { TextPosition } from '../types/intelligenceTypes'
import { FileType<PERSON>enderer, fileTypeRegistry } from './FileTypeRenderer' // YOLO: Added plugin system
import { detectFileType, FileTypeInfo, requiresElectronProcessing } from '../services/fileTypeRegistry' // Use unified detection
import { unifiedPathService } from '../services/unifiedPathService'

interface DocumentViewerProps {
  filePath: string
  fileName: string
  onTextSelection?: (selectedText: string, position: TextPosition) => void
  onContentLoad?: (content: string) => void
  onContentExtracted?: (extractedContent: string, metadata: any) => void
  onClose?: () => void // YOLO: Added close handler

}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  filePath,
  fileName,
  onTextSelection,
  onContentLoad,
  onContentExtracted,
  onClose
}) => {
  const [content, setContent] = useState<string>('')
  const [_extractedContent, _setExtractedContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [zoom, setZoom] = useState(100)
  const [selectedText, setSelectedText] = useState<string>('')
  const [fileTypeInfo, setFileTypeInfo] = useState<FileTypeInfo | null>(null)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [showFullTextModal, setShowFullTextModal] = useState(false)
  const [fullTextContent, setFullTextContent] = useState<string>('')
  const [isEditMode, setIsEditMode] = useState(false)
  const [editContent, setEditContent] = useState('')
  const contentRef = useRef<HTMLDivElement>(null)
  const menuRef = useRef<HTMLDivElement>(null)

  // Helper function to extract filename from full path (kept for potential future use)
  // const getFileName = (filePath: string): string => {
  //   try {
  //     return path.basename(filePath)
  //   } catch {
  //     // Fallback for edge cases
  //     const slash = filePath.lastIndexOf('/')
  //     const backslash = filePath.lastIndexOf('\\')
  //     const cut = Math.max(slash, backslash)
  //     return cut > 0 ? filePath.slice(cut + 1) : filePath
  //   }
  // }



  // File type detection now uses unified service

  // Initialize file type detection
  useEffect(() => {
    if (!filePath || !fileName) return

    console.log('[Data-Pipeline] [Detect] filePath=', filePath, 'fileName=', fileName)
    
    // Validate file path using unified path service
    const validatePath = async () => {
      try {
        const pathResult = await unifiedPathService.resolvePath(filePath)
        if (!pathResult.success) {
          console.error('[DocumentViewer] 🚨 Invalid file path:', pathResult.error)
          setError(`Invalid file path: ${pathResult.error}`)
          return
        }
        
        console.log('[DocumentViewer] ✅ Path validated:', pathResult.resolvedPath)
        
        const typeInfo = detectFileType(fileName)
        console.log('[Data-Pipeline] [Detect] detected type=', typeInfo.type, 'requiresProcessing=', typeInfo.requiresProcessing)
        setFileTypeInfo(typeInfo)
        loadFileContent(typeInfo)
      } catch (error) {
        console.error('[DocumentViewer] ❌ Path validation failed:', error)
        setError('Path validation failed')
      }
    }
    
    validatePath()
  }, [filePath, fileName])

  const loadFileContent = useCallback(async (typeInfo?: FileTypeInfo) => {
    if (!filePath || !fileName) return

    console.log('[Flow1] 📄 DocumentViewer.loadFileContent called')
    console.log('[Flow1] 📄 filePath:', filePath)
    console.log('[Flow1] 📄 fileName:', fileName)
    console.log('[Flow1] 📄 typeInfo:', typeInfo)

    setIsLoading(true)
    setError(null)

    // Validate path again before loading content
    const pathResult = await unifiedPathService.resolvePath(filePath)
    if (!pathResult.success) {
      setError(`Path validation failed: ${pathResult.error}`)
      setIsLoading(false)
      return
    }

    const validatedFilePath = pathResult.resolvedPath
    const fileType = typeInfo || detectFileType(fileName)

    try {
      // Load display content
      await loadDisplayContent(fileType)

      // Extract content for AI if possible
      // For PDFs, always attempt extraction regardless of display content
      if (fileType.canExtractText || fileType.type === 'pdf') {
        await extractContentForAI(fileType)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load file')
      console.error('DocumentViewer: Failed to load file content:', err)
    } finally {
      setIsLoading(false)
    }
  }, [filePath, fileName])

  const loadDisplayContent = async (fileType: FileTypeInfo) => {
    // Use validated file path from loadFileContent
    const validatedFilePath = await unifiedPathService.resolvePath(filePath).then(r => r.success ? r.resolvedPath : filePath)
    
    console.log('DocumentViewer: Loading file content for:', validatedFilePath, 'Type:', fileType.type)
    console.log('[Data-Pipeline] [Display] start load method for', fileType.type)

    // For Office docs, prefer extracted text from kernel processors for UI display
    const isOffice = ['word', 'excel', 'powerpoint'].includes(fileType.type)
    if (isOffice && window.electronAPI?.files?.processFile) {
      try {
        console.log('[Flow1] 🔄 Office document detected, calling processFile')
        console.log('[Flow1] 🔄 isOffice:', isOffice, 'fileType:', fileType.type)
        console.log('[Flow1] 🔄 filePath for processFile:', filePath)
        const proc = await window.electronAPI.files.processFile(filePath)
        console.log('[Flow1] 🔄 processFile response:', proc)
        console.log('[Flow1] 🔄 processFile success:', proc?.success)
        console.log('[Flow1] 🔄 processFile has content.text:', !!(proc?.content?.text))
        
        if (proc?.success && proc?.content?.text) {
          const extractedText = proc.content.text as string
          const metadata = proc.content.metadata || {}
          setContent(extractedText)
          onContentLoad?.(extractedText)
          // Also forward to extraction listeners since this is the same payload
          console.log('[Flow1] ✅ Office text extracted successfully, length:', extractedText.length)
          console.log('[Flow1] ✅ Calling onContentExtracted with metadata:', {
            extractionMethod: 'electron-processor',
            pluginMetadata: metadata,
            frontmatter: metadata?.frontmatter,
            stats: metadata?.stats,
            processingConfidence: metadata?.processingConfidence,
          })
          onContentExtracted?.(extractedText, {
            extractionMethod: 'electron-processor',
            pluginMetadata: metadata,
            frontmatter: metadata?.frontmatter,
            stats: metadata?.stats,
            processingConfidence: metadata?.processingConfidence,
            timestamp: new Date().toISOString()
          })
          return
        }
        console.warn('[Data-Pipeline] [Display] Office processFile had no text; fall back to file read')
      } catch (err) {
        console.warn('[Data-Pipeline] [Display] Office processFile failed; fall back to file read', err)
      }
    }

    if (window.electronAPI?.vault?.readFile) {
      try {
        // Binary formats must be read as base64 to avoid UTF-8 corruption
        const canReadBase64 = (window as any).electronAPI?.vault?.readFileBase64
        const shouldReadBase64 = ['pdf', 'image'].includes(fileType.type)
        if (shouldReadBase64 && canReadBase64) {
          const result = await (window as any).electronAPI.vault.readFileBase64(filePath)
          console.log('[Data-Pipeline] [Display] base64 read result success=', result?.success, 'type=', fileType.type)
          if (result?.success && (result as any).contentBase64) {
            const base64 = (result as any).contentBase64 as string
            // For PDFs, don't set display content to raw binary data
            // This will be handled by the PDF renderer plugin
            if (fileType.type !== 'pdf') {
              setContent(base64)
              onContentLoad?.(base64)
            }
            console.log('[Data-Pipeline] [Display] base64 length=', base64?.length, 'type=', fileType.type)
            return
          }
          // Fallback to normal read if base64 not available
          console.warn('[Data-Pipeline] [Display] base64 path unavailable or failed; falling back to text read for', fileType.type)
        }

        const result = await window.electronAPI.vault.readFile(filePath)
        console.log('[Data-Pipeline] [Display] text read success=', result?.success, 'length=', result?.content?.length)

        if (result?.success && typeof result.content === 'string') {
          // For PDFs, don't set display content to raw binary data
          if (fileType.type !== 'pdf') {
            setContent(result.content)
            onContentLoad?.(result.content)
          }
          console.log('DocumentViewer: Content loaded successfully, length:', result.content.length)
        } else {
          console.error('DocumentViewer: File read failed:', result?.error)
          throw new Error(result?.error || 'Failed to read file')
        }
      } catch (error) {
        console.error('DocumentViewer: Exception during file read:', error)
        console.log('[Data-Pipeline] [Display] error reading content:', (error as any)?.message)
        throw error
      }
    } else {
      // Fallback for development
      console.log('DocumentViewer: Using mock content for development')
      const mockContent = generateMockContent(fileType)
      setContent(mockContent)
      onContentLoad?.(mockContent)
    }
  }

  const extractContentForAI = async (fileType: FileTypeInfo) => {
    // For PDFs, allow extraction even without display content
    if (!fileType.canExtractText && fileType.type !== 'pdf') return
    if (fileType.type !== 'pdf' && !content) return

    setIsProcessing(true)
    try {
      let extractedText = ''
      let metadata = {}

      console.log('[Data-Pipeline] [Extract] start for', fileType.type)

      const shouldUseKernel = requiresElectronProcessing(fileType.type) || fileType.requiresProcessing
      console.log('[Data-Pipeline] [Extract] DEBUG - fileType:', fileType.type, 'requiresElectronProcessing:', requiresElectronProcessing(fileType.type), 'requiresProcessing:', fileType.requiresProcessing, 'shouldUseKernel:', shouldUseKernel)
      
      // For PDFs, always attempt extraction regardless of content
      if (fileType.type === 'pdf' || shouldUseKernel) {
        console.log('[Data-Pipeline] [Extract] route=kernel', { type: fileType.type })
        
        // Use validated file path
        const validatedFilePath = await unifiedPathService.resolvePath(filePath).then(r => r.success ? r.resolvedPath : filePath)
        const result = await window.electronAPI.files.processFile(validatedFilePath)
        
        // DEBUG: Log the exact response structure
        console.log('[Data-Pipeline] [Extract] DEBUG - Full API response:', JSON.stringify(result, null, 2))
        console.log('[Data-Pipeline] [Extract] DEBUG - Response type:', typeof result)
        console.log('[Data-Pipeline] [Extract] DEBUG - Has content:', !!result?.content)
        console.log('[Data-Pipeline] [Extract] DEBUG - Content type:', typeof result?.content)
        console.log('[Data-Pipeline] [Extract] DEBUG - Has text:', !!result?.content?.text)
        console.log('[Data-Pipeline] [Extract] DEBUG - Text type:', typeof result?.content?.text)
        if (result?.content?.text) {
          console.log('[Data-Pipeline] [Extract] DEBUG - First 200 chars of text:', result.content.text.substring(0, 200))
          console.log('[Data-Pipeline] [Extract] DEBUG - Text contains binary:', /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(result.content.text))
        }
        
        // Fix: Use the correct response format
        let extractedText = result?.content?.text || ''
        let metadata = result?.content?.metadata || {}
        console.log('[Data-Pipeline] [Extract] kernel result', { success: result?.success, hasText: !!extractedText, textLen: extractedText?.length })
        if (result?.success && extractedText) {
          metadata = {
            extractionMethod: 'electron-processor',
            pluginMetadata: metadata,
            frontmatter: (metadata as any)?.frontmatter,
            stats: (metadata as any)?.stats,
            processingConfidence: (metadata as any)?.processingConfidence,
            timestamp: new Date().toISOString()
          }
          
          // For PDFs, set the extracted text as the display content
          if (fileType.type === 'pdf') {
            setContent(extractedText)
            onContentLoad?.(extractedText)
            console.log('[Data-Pipeline] [Extract] PDF extracted text set as display content, length:', extractedText.length)
          }
        } else {
          console.log('[Data-Pipeline] [Extract] kernel fallback to display content')
          extractedText = content || ''
          metadata = {
            extractionMethod: 'display-content-fallback',
            timestamp: new Date().toISOString()
          }
        }
      } else {
        // Renderer-side extraction only for simple text/markdown/code
        console.log('[Data-Pipeline] [Extract] route=renderer-plugin', { type: fileType.type })
        const pluginResult = await fileTypeRegistry.extractContent(fileType, filePath, content)
        if (pluginResult) {
          extractedText = pluginResult.text
          metadata = pluginResult.metadata
        } else {
          extractedText = content || ''
          metadata = {
            extractionMethod: 'direct-read',
            stats: {
              wordCount: (content || '').split(/\s+/).filter(Boolean).length,
              lineCount: (content || '').split('\n').length,
              charCount: (content || '').length
            },
            timestamp: new Date().toISOString()
          }
        }
      }

      if (extractedText) {
        _setExtractedContent(extractedText)
        console.log('[Data-Pipeline] [Extract] emit onContentExtracted len=', extractedText.length, 'method=', (metadata as any)?.extractionMethod)
        onContentExtracted?.(extractedText, metadata)
      }
    } catch (err) {
      console.warn('Content extraction failed:', err)
      console.log('[Data-Pipeline] [Extract] error:', (err as any)?.message)
      // Fallback to display content
      _setExtractedContent(content || '')
      onContentExtracted?.(content || '', {
        type: fileType.type,
        extractionMethod: 'fallback',
        error: err instanceof Error ? err.message : 'Unknown error'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const generateMockContent = (fileType: FileTypeInfo): string => {
    if (fileType.type === 'pdf') {
      return `# Project Specification Document

## Overview
This comprehensive project specification document outlines the complete design system requirements for the ChatLo application. It includes detailed UI components, color schemes, typography guidelines, and interaction patterns.

## Key Components

### 1. Design System
- Color palette: Primary #8AB0BB, Secondary #FF8383, Tertiary #1B3E68
- Typography: Inter font family
- Component library with reusable elements

### 2. User Interface Requirements
- Dark theme implementation
- Responsive design patterns
- Accessibility compliance
- Modern interaction patterns

### 3. Technical Specifications
- React-based component architecture
- TypeScript implementation
- Tailwind CSS styling
- FontAwesome icon integration

## Implementation Guidelines
The design system should maintain consistency across all application components while providing flexibility for future enhancements.`
    } else if (fileType.type === 'markdown') {
      return `# Documentation File

This is a markdown document containing important information about the project.

## Features
- Comprehensive documentation
- Code examples
- API references
- Installation guides

## Usage
Follow the guidelines provided in this document for proper implementation.

### Code Example
\`\`\`typescript
const example = {
  type: 'markdown',
  canExtractText: true,
  canAnnotate: true
}
\`\`\`

### Important Notes
> This is a blockquote with important information
> that spans multiple lines.

**Bold text** and *italic text* are supported.`
    } else if (fileType.type === 'text') {
      return `File content for ${fileName}

This is the content of the selected file. The actual content would be loaded from the file system.

Key information:
- File type: ${fileType.extension}
- File path: ${filePath}
- Content type: Text-based document
- Can extract text: ${fileType.canExtractText}
- Can annotate: ${fileType.canAnnotate}

This text file contains plain text content that can be easily processed and analyzed by AI systems.`
    } else if (fileType.type === 'code') {
      return `// ${fileName}
// This is a ${fileType.extension} file

export interface FileTypeInfo {
  type: 'pdf' | 'markdown' | 'text' | 'image' | 'code' | 'unsupported'
  extension: string
  mimeType?: string
  canExtractText: boolean
  canAnnotate: boolean
  requiresProcessing: boolean
  extractionMethod: string
  displayName: string
}

// Example function
function detectFileType(fileName: string): FileTypeInfo {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  // Implementation details...
  return fileTypeInfo
}`
    } else if (fileType.type === 'image') {
      return `[Image File: ${fileName}]

This is an image file that would be displayed visually.
Text extraction is possible through OCR processing.

Image properties:
- Format: ${fileType.extension.toUpperCase()}
- MIME Type: ${fileType.mimeType}
- OCR Available: ${fileType.canExtractText}

Note: Actual image content would be displayed in the viewer.`
    } else {
      return `Unsupported file type: ${fileName}

This file type (${fileType.extension}) is not currently supported for viewing or text extraction.

File information:
- Extension: ${fileType.extension}
- Display Name: ${fileType.displayName}
- Can Extract Text: ${fileType.canExtractText}
- Can Annotate: ${fileType.canAnnotate}`
    }
  }

  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      const selectedText = selection.toString().trim()
      setSelectedText(selectedText)
      
      // Calculate position information
      const range = selection.getRangeAt(0)
      const position: TextPosition = {
        start_offset: range.startOffset,
        end_offset: range.endOffset,
        line_number: 1, // Would need more complex calculation for actual line numbers
        column_number: 1
      }
      
      onTextSelection?.(selectedText, position)
    }
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 50))
  }

  const resetZoom = () => {
    setZoom(100)
  }

  // New handler functions for enhanced UI
  const handleViewFullText = () => {
    const contentToShow = _extractedContent || content
    setFullTextContent(contentToShow)
    setShowFullTextModal(true)
    // Auto-open menu to invite user action
    setIsMenuOpen(true)
  }

  const handleModalScroll = () => {
    // Auto-close menu when user scrolls in modal
    setIsMenuOpen(false)
  }

  const handleCopyExtractText = async () => {
    try {
      const textToCopy = _extractedContent || content
      await navigator.clipboard.writeText(textToCopy)
      console.log('Text copied to clipboard successfully')
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text to clipboard:', err)
    }
  }

  // Smart system app opener based on file type
  const handleOpenInSystemApp = () => {
    if (window.electronAPI?.shell?.openPath) {
      window.electronAPI.shell.openPath(filePath)
    } else {
      console.warn('External app opening not available in this environment')
    }
  }

  // Get app name for system app based on file type
  const getSystemAppName = (fileType: string): string => {
    switch (fileType) {
      case 'word': return 'Word'
      case 'excel': return 'Excel'
      case 'powerpoint': return 'PowerPoint'
      case 'pdf': return 'System App'
      case 'markdown': return 'System App'
      case 'image': return 'System App'
      case 'code': return 'System App'
      default: return 'System App'
    }
  }

  // Handle markdown editing (for markdown files only) - removed as editing is now handled inline
  // const handleEditMarkdown = () => {
  //   // This could open an integrated markdown editor or external editor
  //   if (window.electronAPI?.shell?.openPath) {
  //     window.electronAPI.shell.openPath(filePath)
  //   } else {
  //     console.warn('Markdown editing not available in this environment')
  //   }
  // }

  const handleBackToTreeView = () => {
    onClose?.()
  }

  const handleEdit = () => {
    setIsEditMode(true)
    setEditContent(content)
  }

  const handleSave = async () => {
    if (!filePath) return
    
    try {
      if (window.electronAPI?.vault?.writeFile) {
        const result = await window.electronAPI.vault.writeFile(filePath, editContent)
        if (result.success) {
          setContent(editContent)
          setIsEditMode(false)
          // Trigger content update
          onContentLoad?.(editContent)
        } else {
          throw new Error(result.error || 'Failed to save file')
        }
      } else {
        throw new Error('File write API not available')
      }
    } catch (error) {
      console.error('Failed to save file:', error)
      alert('Failed to save file. Please try again.')
    }
  }

  const handleCancel = () => {
    setIsEditMode(false)
    setEditContent(content)
  }

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false)
      }
    }

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isMenuOpen])

  // ESC key to go back using the same close logic
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose?.()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onClose])

  // const _getFileIcon = () => {
  //   if (!fileTypeInfo) return ICONS.file
  //   switch (fileTypeInfo.type) {
  //     case 'pdf': return ICONS.filePdf
  //     case 'markdown':
  //     case 'text': return ICONS.fileText
  //     case 'image': return ICONS.fileImage
  //     case 'code': return ICONS.fileCode
  //     default: return ICONS.file
  //   }
  // }

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-400">
            <FontAwesomeIcon icon={ICONS.spinner} className="text-4xl mb-4 animate-spin" />
            <p className="text-lg font-medium">Loading document...</p>
            <p className="text-sm">Please wait while we load the content</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-red-400">
            <FontAwesomeIcon icon={ICONS.exclamationTriangle} className="text-4xl mb-4" />
            <p className="text-lg font-medium">Failed to load document</p>
            <p className="text-sm">{error}</p>
            <button
              onClick={() => loadFileContent()}
              className="mt-4 px-4 py-2 bg-secondary hover:bg-secondary/80 text-gray-900 rounded-lg transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      )
    }

    if (!fileTypeInfo) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-400">
            <FontAwesomeIcon icon={ICONS.file} className="text-4xl mb-4" />
            <p className="text-lg font-medium">Detecting file type...</p>
          </div>
        </div>
      )
    }

    // YOLO: REPLACED WITH FileTypeRenderer PLUGIN SYSTEM!
    // All file type rendering is now handled by the plugin system
    return (
      <FileTypeRenderer
        content={content}
        fileTypeInfo={fileTypeInfo}
        filePath={filePath}
        fileName={fileName}
        isLoading={false} // Loading is handled above
        onContentChange={(newContent) => {
          // Handle content changes if needed
          console.log('Content changed:', newContent.length, 'characters');
        }}
        onTextExtracted={(text) => {
          // Handle text extraction if needed
          console.log('Text extracted:', text.length, 'characters');
        }}
        // Pass additional props for specific renderers
        zoom={zoom}
        onTextSelection={handleTextSelection}
        contentRef={contentRef}
      />
    );
  }

  // YOLO: REMOVED renderPDFContent - Now handled by PDFRenderer plugin

  // YOLO: REMOVED renderMarkdownContent - Now handled by MarkdownRenderer plugin

  // YOLO: REMOVED renderTextContent - Now handled by TextRenderer plugin

  // YOLO: REMOVED renderImageContent - Now handled by ImageRenderer plugin

  // YOLO: REMOVED renderUnsupportedContent - Now handled by UnsupportedRenderer plugin

  // const _renderMarkdownToHTML = (markdown: string): string => {
  //   let html = markdown
  //   html = html.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold text-gray-800 mt-6 mb-3">$1</h3>')
  //   html = html.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold text-gray-800 mt-8 mb-4">$1</h2>')
  //   html = html.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-gray-800 mt-8 mb-6">$1</h1>')
  //   html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
  //   html = html.replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
  //   html = html.replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-3 rounded text-sm overflow-x-auto"><code>$1</code></pre>')
  //   html = html.replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded text-sm">$1</code>')
  //   html = html.replace(/^- (.*$)/gim, '<li class="ml-4">$1</li>')
  //   html = html.replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-gray-300 pl-4 italic text-gray-600">$1</blockquote>')
  //   html = html.replace(/\n\n/g, '</p><p class="mb-4">')
  //   html = '<p class="mb-4">' + html + '</p>'
  //   return html
  // }

  return (
    <div 
      className="flex-1 flex flex-col bg-gray-900 text-gray-300 overflow-hidden"
      data-file-path={filePath}
      data-file-name={fileName}
    >
      {/* Document Viewer Header */}
      <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
        <div className="flex items-center gap-3">
          {/* Back button - unified behavior like close button */}
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105"
            title="Back"
          >
            <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-gray-400 text-lg" />
          </button>
 
          <div className="flex flex-col">
            <div className="flex items-center gap-3 text-xs text-gray-400">
              <span>{fileTypeInfo?.displayName || 'Document'}</span>
              <span className="flex items-center gap-1">
                <FontAwesomeIcon icon={ICONS.robot} className="text-xs" />
                AI Ready
              </span>
              {isProcessing && (
                <span className="flex items-center gap-1 text-yellow-400">
                  <FontAwesomeIcon icon={ICONS.spinner} className="animate-spin text-xs" />
                  Processing...
                </span>
              )}
              {/* Show extraction status for PDFs */}
              {fileTypeInfo?.type === 'pdf' && _extractedContent && (
                <span className="flex items-center gap-1 text-green-400">
                  <FontAwesomeIcon icon={ICONS.checkCircle} className="text-xs" />
                  Text Extracted ({_extractedContent.length.toLocaleString()} chars)
                </span>
              )}
              {/* Show extraction in progress for PDFs */}
              {fileTypeInfo?.type === 'pdf' && isProcessing && (
                <span className="flex items-center gap-1 text-blue-400">
                  <FontAwesomeIcon icon={ICONS.spinner} className="animate-spin text-xs" />
                  Extracting Text...
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button 
            onClick={handleZoomOut}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.magnifyingGlassMinus} className="text-gray-400 text-sm" />
          </button>
          <button 
            onClick={resetZoom}
            className="px-3 py-1 hover:bg-gray-700 rounded transition-colors"
          >
            <span className="text-gray-400 text-sm">{zoom}%</span>
          </button>
          <button 
            onClick={handleZoomIn}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.magnifyingGlassPlus} className="text-gray-400 text-sm" />
          </button>
          {/* Eye icon for full text viewing */}
          <button 
            onClick={handleViewFullText}
            className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
            title="View full extracted text"
          >
            <FontAwesomeIcon icon={ICONS.eye} className="text-gray-400 text-sm" />
          </button>
 
          {/* Hamburger menu */}
          <div className="relative" ref={menuRef}>
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 hover:bg-gray-700/80 rounded-lg transition-all duration-200 hover:scale-105"
              title="Menu"
            >
              <FontAwesomeIcon icon={ICONS.bars} className="text-gray-400 text-lg" />
            </button>
            
            {/* Smart Menu dropdown - file type aware */}
            {isMenuOpen && (
              <div className="absolute right-0 top-full mt-2 w-56 bg-gray-700 rounded-lg shadow-lg border border-gray-600 z-50">
                <div className="py-2">
                  <button
                    onClick={handleCopyExtractText}
                    className="w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-600 transition-colors flex items-center gap-3"
                  >
                    <FontAwesomeIcon icon={ICONS.clipboard} className="text-sm" />
                    Copy Extract Text
                  </button>
                  
                  {/* Smart system app opener based on file type */}
                  <button
                    onClick={handleOpenInSystemApp}
                    className="w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-600 transition-colors flex items-center gap-3"
                  >
                    <FontAwesomeIcon icon={ICONS.externalLink} className="text-sm" />
                    Open in {getSystemAppName(fileTypeInfo?.type || 'unknown')}
                  </button>
                  
                  <button
                    onClick={handleBackToTreeView}
                    className="w-full px-4 py-2 text-left text-gray-300 hover:bg-gray-600 transition-colors flex items-center gap-3"
                  >
                    <FontAwesomeIcon icon={ICONS.arrowLeft} className="text-sm" />
                    Back
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
       
      {/* Document Controls Separator */}
      <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-600 px-4">
        <div className="flex items-center gap-2">
          {!isEditMode && (
            <>
              <button
                onClick={handleEdit}
                className="px-3 py-1.5 bg-primary hover:bg-primary/80 text-gray-900 rounded text-sm font-medium transition-colors flex items-center gap-2"
              >
                <FontAwesomeIcon icon={ICONS.edit} className="text-xs" />
                Edit
              </button>
              {/* Manual text extraction for PDFs */}
              {fileTypeInfo?.type === 'pdf' && (
                <button
                  onClick={() => extractContentForAI(fileTypeInfo)}
                  disabled={isProcessing}
                  className="px-3 py-1.5 bg-green-600 hover:bg-green-500 disabled:bg-gray-600 text-white rounded text-sm font-medium transition-colors flex items-center gap-2"
                >
                  <FontAwesomeIcon icon={ICONS.fileText} className="text-xs" />
                  {isProcessing ? 'Extracting...' : 'Extract Text'}
                </button>
              )}
            </>
          )}
        </div>
        <div className="flex items-center gap-2">
          {isEditMode && (
            <>
              <button
                onClick={handleCancel}
                className="px-3 py-1.5 bg-gray-600 hover:bg-gray-500 text-gray-300 rounded text-sm transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-3 py-1.5 bg-primary hover:bg-primary/80 text-gray-900 rounded text-sm font-medium transition-colors"
              >
                Save
              </button>
            </>
          )}
        </div>
      </div>

      {/* PDF Extracted Text Display */}
      {fileTypeInfo?.type === 'pdf' && _extractedContent && (
        <div className="mx-4 mb-4 p-4 bg-green-900/20 border border-green-600/30 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <FontAwesomeIcon icon={ICONS.fileText} className="text-green-400 text-sm" />
            <span className="text-green-400 font-medium text-sm">PDF Text Extracted Successfully</span>
            <span className="text-green-300 text-xs">
              {_extractedContent.length.toLocaleString()} characters
            </span>
          </div>
          <div className="bg-gray-800/50 rounded p-3 max-h-32 overflow-y-auto">
            <pre className="text-green-100 text-xs leading-relaxed whitespace-pre-wrap font-mono">
              {_extractedContent.substring(0, 500)}
              {_extractedContent.length > 500 && '...'}
            </pre>
          </div>
          <div className="mt-2 flex items-center gap-2 text-xs text-green-300">
            <FontAwesomeIcon icon={ICONS.robot} className="text-xs" />
            <span>Ready for AI processing and analysis</span>
          </div>
        </div>
      )}

      {/* Document Content */}
      <div className="flex-1 bg-gray-900 p-4 overflow-auto">
        {isEditMode ? (
          <div className="max-w-4xl mx-auto">
            <textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="w-full h-96 bg-gray-800 text-gray-100 p-4 rounded-lg border border-gray-600 focus:border-primary focus:outline-none font-mono text-sm resize-none"
              placeholder="Edit your content here..."
            />
          </div>
        ) : (
          renderContent()
        )}
      </div>
 
      {/* Text Selection Indicator */}
      {selectedText && (
        <div className="p-2 bg-primary/10 border-t border-primary/20">
          <div className="flex items-center gap-2">
            <FontAwesomeIcon icon={ICONS.textSelect} className="text-primary text-sm" />
            <span className="text-primary text-xs font-medium">Selected:</span>
            <span className="text-supplement1 text-xs truncate flex-1">
              "{selectedText.length > 50 ? selectedText.substring(0, 50) + '...' : selectedText}"
            </span>
            <button 
              onClick={() => setSelectedText('')}
              className="p-1 hover:bg-primary/20 rounded transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.xmark} className="text-primary text-xs" />
            </button>
          </div>
        </div>
      )}

      {/* Full Text Modal */}
      {showFullTextModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl border border-gray-600 w-[90vw] h-[80vh] flex flex-col">
            {/* Modal Header */}
            <div className="p-4 border-b border-gray-600 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <FontAwesomeIcon icon={ICONS.eye} className="text-primary text-lg" />
                <h3 className="text-lg font-semibold text-supplement1">Full Extracted Text</h3>
                <span className="text-xs text-gray-400">
                  {fullTextContent.length.toLocaleString()} characters
                </span>
              </div>
              <button
                onClick={() => setShowFullTextModal(false)}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                title="Close modal"
              >
                <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400" />
              </button>
            </div>
            
            {/* Modal Content */}
            <div 
              className="flex-1 p-4 overflow-auto bg-gray-900"
              onScroll={handleModalScroll}
            >
              <pre className="text-gray-300 text-sm leading-relaxed whitespace-pre-wrap font-mono">
                {fullTextContent}
              </pre>
            </div>
            
            {/* Modal Footer */}
            <div className="p-4 border-t border-gray-600 flex items-center justify-between">
              <div className="text-xs text-gray-400">
                Scroll to auto-close menu • Click outside to close modal
              </div>
              <div className="flex gap-2">
                <button
                  onClick={handleCopyExtractText}
                  className="px-3 py-1 bg-primary hover:bg-primary/80 text-gray-900 rounded transition-colors text-sm flex items-center gap-2"
                >
                  <FontAwesomeIcon icon={ICONS.clipboard} className="text-xs" />
                  Copy Text
                </button>
                <button
                  onClick={() => setShowFullTextModal(false)}
                  className="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-gray-300 rounded transition-colors text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
