/**
 * useFileIntelligence Hook - Unified Data Management for File Intelligence
 * 
 * YOLO Phase 3: Centralized data management with unified JSON format
 * Extracted from FilePageOverlay for better architecture
 */

import { useState, useEffect, useCallback } from 'react';
import {
  KeyIdea,
  ProcessingResult,
  FileIntelligence,
  SmartAnnotationNote
} from '../types/fileIntelligenceTypes';
import { unifiedAnnotationService } from '../services/unifiedAnnotationService';
import { useSession } from '../contexts/SessionContext';

// ============================================================================
// UNIFIED JSON FORMAT - Single Source of Truth
// ============================================================================

export interface UnifiedFileIntelligenceData {
  // File metadata
  file: {
    path: string | null;
    name: string | null;
    content: string;
    type: string | null;
    size: number;
    lastModified: string;
  };
  
  // Intelligence processing results
  intelligence: {
    keyIdeas: KeyIdea[];
    summary: string;
    confidence: number;
    processingStatus: 'idle' | 'processing' | 'complete' | 'error';
    lastProcessed: string | null;
    error: string | null;
    fileIntelligence: FileIntelligence | null;
  };
  
  // Annotation system
  annotations: {
    notes: SmartAnnotationNote[];
    currentIndex: number;
    isEditing: boolean;
    editContent: string;
    customPrompt: string;
    isProcessing: boolean;
    showExamples: boolean;
  };
  
  // UI state
  ui: {
    selectedLabels: string[];
    expandedSections: string[];
    viewMode: 'overview' | 'detailed' | 'annotations';
  };
  
  // Metadata
  metadata: {
    version: string;
    createdAt: string;
    updatedAt: string;
    sessionId: string;
  };
}

// ============================================================================
// DEFAULT STATE FACTORY
// ============================================================================

const createDefaultData = (filePath?: string, fileName?: string): UnifiedFileIntelligenceData => ({
  file: {
    path: filePath || null,
    name: fileName || null,
    content: '',
    type: null,
    size: 0,
    lastModified: new Date().toISOString(),
  },
  intelligence: {
    keyIdeas: [],
    summary: '',
    confidence: 0,
    processingStatus: 'idle',
    lastProcessed: null,
    error: null,
    fileIntelligence: null,
  },
  annotations: {
    notes: [],
    currentIndex: 0,
    isEditing: false,
    editContent: '',
    customPrompt: '',
    isProcessing: false,
    showExamples: false,
  },
  ui: {
    selectedLabels: [],
    expandedSections: ['intelligence', 'annotations'],
    viewMode: 'overview',
  },
  metadata: {
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  },
});

// ============================================================================
// UNIFIED FILE INTELLIGENCE HOOK
// ============================================================================

export const useFileIntelligence = (filePath?: string | null, fileName?: string | null) => {
  const { currentVault } = useSession();
  const [data, setData] = useState<UnifiedFileIntelligenceData>(() =>
    createDefaultData(filePath || undefined, fileName || undefined)
  );

  // Initialize when file path changes
  useEffect(() => {
    if (filePath && fileName) {
      setData(prev => ({
        ...createDefaultData(filePath, fileName),
        file: {
          ...createDefaultData(filePath, fileName).file,
          content: prev.file.content, // Preserve existing content
        }
      }));
    }
  }, [filePath, fileName]);

  // Update file content
  const updateFileContent = useCallback((content: string) => {
    setData(prev => ({
      ...prev,
      file: {
        ...prev.file,
        content,
        size: content.length,
        lastModified: new Date().toISOString(),
      },
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString(),
      },
    }));
  }, []);

  // Update file content with plugin metadata
  const updateFileContentWithMetadata = useCallback(async (content: string, pluginMetadata?: any) => {
    console.log('[Flow1] 🔄 useFileIntelligence.updateFileContentWithMetadata called')
    console.log('[Flow1] 🔄 content length:', content.length)
    console.log('[Flow1] 🔄 pluginMetadata:', pluginMetadata)
    console.log('[Flow1] 🔄 filePath:', filePath)
    console.log('[Flow1] 🔄 fileName:', fileName)
    console.log('[Flow1] 🔄 currentVault:', currentVault)
    console.log('[Flow1] 🔄 About to call intelligence:write...')

    // FLOW1 FIX: Save extracted content to intelligence system
    if (filePath && currentVault?.path && content) {
      try {
        console.log('[Flow1] 💾 Saving extracted content to intelligence system...')

        // Create basic intelligence data structure with extracted content
        const intelligenceData = {
          file_path: filePath,
          key_ideas: [], // Empty for now, will be populated by AI analysis later
          weighted_entities: [],
          human_connections: [],
          processing_confidence: 0.95, // High confidence for direct text extraction
          analysis_metadata: {
            processing_time_ms: 0,
            model_used: 'file-extraction',
            timestamp: new Date().toISOString(),
            extraction_method: pluginMetadata?.extractionMethod || 'plugin-system',
            plugin_metadata: pluginMetadata?.pluginMetadata,
            frontmatter: pluginMetadata?.frontmatter,
            stats: pluginMetadata?.stats,
            extracted_text_length: content.length
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          extracted_content: content // Store the extracted text
        }

        const result = await window.electronAPI.intelligence.write(filePath, currentVault.path, {
          json: intelligenceData
        })

        console.log('[Flow1] 💾 Intelligence write result:', result)

        if (result.success) {
          console.log('[Flow1] ✅ Extracted content saved to intelligence system successfully!')
        } else {
          console.error('[Flow1] 🚨 Failed to save extracted content:', result.error)
        }
      } catch (error: any) {
        console.error('[Flow1] 🚨 Error saving extracted content to intelligence system:', error.message)
      }
    }

    setData(prev => ({
      ...prev,
      file: {
        ...prev.file,
        content,
        size: content.length,
        lastModified: new Date().toISOString(),
      },
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString(),
      },
      intelligence: {
        ...prev.intelligence,
        fileIntelligence: prev.intelligence.fileIntelligence ? {
          ...prev.intelligence.fileIntelligence,
          analysis_metadata: {
            ...prev.intelligence.fileIntelligence.analysis_metadata,
            plugin_metadata: pluginMetadata ? {
              extraction_method: pluginMetadata.extractionMethod,
              frontmatter: pluginMetadata.frontmatter,
              stats: pluginMetadata.stats,
              plugin_data: pluginMetadata.pluginMetadata
            } : undefined
          }
        } : prev.intelligence.fileIntelligence
      }
    }));
  }, [currentVault]);

  // Update intelligence data
  const updateIntelligence = useCallback((intelligence: Partial<UnifiedFileIntelligenceData['intelligence']>) => {
    setData(prev => ({
      ...prev,
      intelligence: { ...prev.intelligence, ...intelligence },
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString(),
      },
    }));
  }, []);

  // Update annotations
  const updateAnnotations = useCallback((annotations: Partial<UnifiedFileIntelligenceData['annotations']>) => {
    setData(prev => ({
      ...prev,
      annotations: { ...prev.annotations, ...annotations },
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString(),
      },
    }));
  }, []);

  // Update UI state
  const updateUI = useCallback((ui: Partial<UnifiedFileIntelligenceData['ui']>) => {
    setData(prev => ({
      ...prev,
      ui: { ...prev.ui, ...ui },
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString(),
      },
    }));
  }, []);

  // Load annotations from storage
  const loadAnnotations = useCallback(async (path: string) => {
    try {
      updateAnnotations({ isProcessing: true });
      const notes = await unifiedAnnotationService.loadAnnotations(path);
      updateAnnotations({
        notes,
        currentIndex: notes.length > 0 ? notes.length - 1 : 0,
        isProcessing: false
      });
    } catch (error) {
      console.error('[useFileIntelligence] Error loading annotations:', error);
      updateAnnotations({ isProcessing: false });
    }
  }, [updateAnnotations]);

  // Generate and save annotation
  const generateAnnotation = useCallback(async (path: string, prompt: string, content: string) => {
    try {
      updateAnnotations({ isProcessing: true });
      const annotation = await unifiedAnnotationService.generateAnnotation(path, prompt, content);
      if (annotation) {
        const success = await unifiedAnnotationService.saveAnnotation(path, annotation);
        if (success) {
          await loadAnnotations(path); // Reload to get updated list
          updateAnnotations({ customPrompt: '', isProcessing: false });
          return true;
        }
      }
      updateAnnotations({ isProcessing: false });
      return false;
    } catch (error) {
      console.error('[useFileIntelligence] Error generating annotation:', error);
      updateAnnotations({ isProcessing: false });
      return false;
    }
  }, [updateAnnotations, loadAnnotations]);

  // Handle labels changed (from SmartLabelingInterface)
  const handleLabelsChanged = useCallback((allIdeas: KeyIdea[]) => {
    updateIntelligence({ keyIdeas: allIdeas });
  }, [updateIntelligence]);

  // Handle processing complete (from SmartLabelingInterface)
  const handleProcessingComplete = useCallback(async (result: ProcessingResult) => {
    updateIntelligence({
      processingStatus: 'complete',
      lastProcessed: new Date().toISOString(),
      confidence: (result as any).confidence ?? (result as any).confidence_score ?? 0,
    });
  }, [updateIntelligence]);

  // Reset data
  const reset = useCallback(() => {
    setData(createDefaultData(filePath || undefined, fileName || undefined));
  }, [filePath, fileName]);

  // Export data as JSON
  const exportData = useCallback(() => {
    return JSON.stringify(data, null, 2);
  }, [data]);

  // Import data from JSON
  const importData = useCallback((jsonData: string) => {
    try {
      const imported = JSON.parse(jsonData) as UnifiedFileIntelligenceData;
      setData(imported);
      return true;
    } catch (error) {
      console.error('[useFileIntelligence] Error importing data:', error);
      return false;
    }
  }, []);

  return {
    // Data
    data,
    
    // File operations
    updateFileContent,
    updateFileContentWithMetadata,
    
    // Intelligence operations
    updateIntelligence,
    handleLabelsChanged,
    handleProcessingComplete,
    
    // Annotation operations
    updateAnnotations,
    // REMOVED: loadAnnotations - Now handled by IntelligenceHub component
    generateAnnotation,
    
    // UI operations
    updateUI,
    
    // Utility operations
    reset,
    exportData,
    importData,
  };
};
