/**
 * FilePageOverlay - Main Intelligence Hub Component
 *
 * ARCHITECTURE REFACTOR PLAN:
 * ===========================
 *
 * CURRENT STATE: Monolithic component (1,505 lines) handling:
 * - File viewing/rendering (LEFT PANEL)
 * - Intelligence processing (RIGHT PANEL)
 * - Multiple file type handling
 * - Annotation management
 * - State orchestration
 *
 * EXTRACTION TARGETS:
 * ===================
 *
 * 1. DocumentViewer Component (LEFT PANEL)
 *    - Extract: renderFileContent(), file type detection, PDF.js integration
 *    - API: DocumentViewerProps { filePath, content, fileType, onContentChange }
 *    - Lines to extract: ~400-800 (file rendering logic)
 *
 * 2. IntelligenceHub Component (RIGHT PANEL)
 *    - Extract: SmartLabelingInterface, annotation display, summary generation
 *    - API: IntelligenceHubProps { intelligenceData, onDataChange }
 *    - Lines to extract: ~200-400 (intelligence UI logic)
 *
 * 3. FileIntelligenceService (DATA LAYER)
 *    - Extract: intelligence data management, unified JSON format
 *    - API: useFileIntelligence(filePath) hook
 *    - Lines to extract: ~100-200 (data management logic)
 *
 * 4. FileTypeRenderer Plugin System
 *    - Extract: PDF, Markdown, Text, Image renderers
 *    - API: FileTypeRendererProps { fileType, content, options }
 *    - Lines to extract: ~300-500 (rendering logic)
 *
 * UNIFIED JSON FORMAT TARGET:
 * ===========================
 * interface FileIntelligenceData {
 *   file: { path: string; type: string; content: string; }
 *   intelligence: { keyIdeas: KeyIdea[]; annotations: SmartAnnotationNote[]; }
 *   ui: { selectedLabels: string[]; currentAnnotation: number; }
 * }
 */

import React, { useState, useEffect, useRef } from 'react';
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
// import { ICONS } from './Icons/index';
// REMOVED: PDFViewerService - Now handled by DocumentViewer
// REMOVED: fileViewerService - Now handled by navigation service
import { DocumentViewer } from './DocumentViewer'; // YOLO: Added DocumentViewer import
import { IntelligenceHub } from './IntelligenceHub'; // YOLO: Added IntelligenceHub import
import { useFileIntelligence } from '../hooks/useFileIntelligence'; // YOLO: Added unified data management hook
import { detectFileType, FileTypeInfo } from '../services/fileTypeRegistry'; // Use unified detection
import {
  KeyIdea,
  ProcessingResult,
  FileIntelligence,
  // HumanConnection,
  // SmartAnnotationNote
} from '../types/fileIntelligenceTypes';
// Removed: fileAnalysisService - SmartLabelingInterface handles all analysis
// Removed: useAppStore - SmartLabelingInterface handles model selection
import * as pdfjsLib from 'pdfjs-dist';
import { extractContextPath as extractContextPathUtil } from '../utils/vaultPath';
import { askAINavigationService } from '../services/askAINavigationService';
import { navigationManager } from '../services/navigationService'; // NEW: Navigation service integration
// import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'
// import { annotationStorageService } from '../services/annotationStorageService'

// Set up PDF.js worker - use local copy in public directory
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

// REMOVED: decodeBase64ToUTF8 - Now handled by DocumentViewer

interface FilePageOverlayProps {
  onClose: () => void;
}

export const FilePageOverlay: React.FC<FilePageOverlayProps> = ({ onClose }) => {
  console.log('[ANNOTATIONS] 🔍 COMPONENT: FilePageOverlay rendered')

  // ============================================================================
  // NAVIGATION SERVICE INTEGRATION SECTION
  // ============================================================================
  //
  // NEW: Integration with navigation service for stateful navigation
  // This replaces the localStorage-based context management and fileViewerService
  // ============================================================================
  
  const [navigationState, setNavigationState] = useState(navigationManager.getState())

  // Subscribe to navigation service for file overlay state
  useEffect(() => {
    const unsubscribe = navigationManager.subscribe(setNavigationState)
    return unsubscribe
  }, [])

  // Extract file overlay context from navigation state
  const fileOverlay = navigationState.fileOverlay
  const chatContext = fileOverlay?.origin

  // Get file information from navigation state
  const filePath = fileOverlay?.filePath || ''
  const fileName = fileOverlay?.fileName || ''

  // ============================================================================
  // UNIFIED DATA MANAGEMENT SECTION
  // ============================================================================
  //
  // YOLO: REPLACED WITH UNIFIED HOOK!
  // - Single source of truth for all file intelligence data
  // - Unified JSON format for better maintainability
  // - Centralized state management with clear boundaries
  // ============================================================================

  // UNIFIED FILE INTELLIGENCE HOOK
  // Replaces all scattered state management
  const {
    data: intelligenceData,
    updateFileContent,
    updateFileContentWithMetadata,
    updateIntelligence,
    handleLabelsChanged: hookHandleLabelsChanged
  } = useFileIntelligence(filePath, fileName);
  const [fileTypeInfo, setFileTypeInfo] = useState<FileTypeInfo | null>(null);

  // UI STATE DOMAIN
  // Target: Keep in FilePageOverlay orchestrator
  const [availableHeight, setAvailableHeight] = useState<number>(window.innerHeight);

  // REFS DOMAIN
  // Target: Move to respective components

  const promptTextareaRef = useRef<HTMLTextAreaElement>(null); // -> AnnotationSection

  // ============================================================================
  // UTILITY FUNCTIONS SECTION
  // ============================================================================
  //
  // REFACTOR TARGET: Extract to respective components and services
  //
  // EXTRACTION PLAN:
  // - UI utilities -> AnnotationSection component
  // - Intelligence utilities -> IntelligenceHub component
  // - File utilities -> DocumentViewer component
  // - Path utilities -> FileIntelligenceService
  // ============================================================================

  // ANNOTATION UI UTILITIES
  // Target: Move to AnnotationSection component

  // UI RESPONSIVE UTILITIES
  // Target: Keep in FilePageOverlay orchestrator
  useEffect(() => {
    const handleResize = () => {
      setAvailableHeight(window.innerHeight);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // PATH UTILITIES
  // Target: Move to FileIntelligenceService
  const extractContextPath = (filePath: string): string => {
    return extractContextPathUtil(filePath);
  };

  // INTELLIGENCE UTILITIES
  // Target: Move to IntelligenceHub component
  // REMOVED: generateDocumentSummary - Now handled by IntelligenceHub

  // ============================================================================
  // DATA MANAGEMENT SECTION
  // ============================================================================
  //
  // REFACTOR TARGET: Extract to FileIntelligenceService
  // API: const { intelligence, updateIntelligence, addManualLabel } = useFileIntelligence(filePath);
  //
  // CURRENT ISSUES:
  // - Intelligence data scattered across multiple handlers
  // - Manual state synchronization between components
  // - No centralized data validation
  //
  // UNIFIED JSON FORMAT TARGET:
  // interface FileIntelligenceData {
  //   file: { path: string; type: string; content: string; }
  //   intelligence: { keyIdeas: KeyIdea[]; annotations: SmartAnnotationNote[]; }
  //   ui: { selectedLabels: string[]; currentAnnotation: number; }
  //   metadata: { lastUpdated: string; processingConfidence: number; }
  // }
  // ============================================================================

  // REMOVED: handleAddManualLabel - unused and potentially causing data flow issues
  // Manual label functionality is now handled by SmartLabelingInterface directly

  // INTELLIGENCE DATA SYNCHRONIZATION - Now handled by hookHandleLabelsChanged
  // Uses unified annotationStorageService instead of intelligence:listSessions

  // PROCESSING COMPLETION HANDLER
  // Target: Move to FileIntelligenceService
  const handleProcessingComplete = async (result: ProcessingResult): Promise<void> => {
    console.log('🎯 [OVERLAY] Processing complete signal received:', result);
    // SmartLabelingInterface will call handleLabelsChanged with the new data
    // No need to reload here since SmartLabelingInterface is the single source of truth
  };

  // ============================================================================
  // FILE TYPE DETECTION & CONTENT LOADING SECTION
  // ============================================================================
  //
  // REFACTOR TARGET: Extract to DocumentViewer component + FileTypeRenderer plugin system
  // API: const { fileType, content, isLoading } = useDocumentViewer(filePath);
  //
  // CURRENT ISSUES:
  // - Hardcoded file type detection (not extensible)
  // - Mixed content loading strategies in one place
  // - No plugin architecture for new file types
  //
  // PLUGIN SYSTEM TARGET:
  // interface FileTypePlugin {
  //   canHandle(fileName: string): boolean;
  //   loadContent(filePath: string): Promise<string>;
  //   renderContent(content: string): React.ReactNode;
  //   extractText?(content: string): Promise<string>;
  // }
  // ============================================================================

  // FILE TYPE DETECTION - Now uses unified service

  // CONTENT TYPE DETECTION - Now uses unified service

  // GENERIC FILE CONTENT LOADING
  // Target: Extract to DocumentViewer component
  // API: const { content, isLoading, error } = useFileContent(filePath, fileType);
  // REMOVED: loadGenericFileContent - Now handled by DocumentViewer

  // ============================================================================
  // PDF LOADING & RENDERING SECTION
  // ============================================================================
  //
  // REFACTOR TARGET: Extract to PDFRenderer component
  // API: const { pdfDocument, currentPage, totalPages, isReady } = usePDFRenderer(filePath);
  //
  // CURRENT ISSUES:
  // - PDF.js integration mixed with component logic
  // - Canvas rendering logic in main component
  // - No separation between PDF loading and rendering
  //
  // EXTRACTION PLAN:
  // 1. Create PDFRenderer component with canvas management
  // 2. Create usePDFDocument hook for PDF.js integration
  // 3. Move all PDF-specific state to PDFRenderer
  // ============================================================================

  // PDF DOCUMENT LOADING
  // REMOVED: loadPDFWithPDFJS - Now handled by DocumentViewer

  // MAIN CONTENT LOADER ORCHESTRATOR - REMOVED
  // Now handled by DocumentViewer component

  // REMOVED: renderPage - Now handled by DocumentViewer PDFRenderer

  // ============================================================================
  // EVENT HANDLERS SECTION
  // ============================================================================
  //
  // REFACTOR TARGET: Distribute to respective components
  //
  // EXTRACTION PLAN:
  // - File operations -> DocumentViewer component
  // - PDF operations -> PDFRenderer component
  // - Intelligence operations -> IntelligenceHub component
  // - Annotation operations -> AnnotationSection component
  // ============================================================================

  // MAIN OVERLAY HANDLERS
  // Target: Keep in FilePageOverlay orchestrator
  const handleClose = () => {
    // Use navigation service to close file overlay
    navigationManager.closeFileOverlay()
    
    // Call the onClose prop
    onClose()
  }

  // NEW: Handle back navigation using navigation service
  const handleBackToOrigin = () => {
    if (chatContext) {
      if (chatContext.page === 'chat') {
        // Return to chat
        const returnTarget = {
          page: 'chat',
          context: chatContext.context,
          resource: chatContext.conversationId,
          params: {}
        }
        navigationManager.navigateTo(returnTarget)
      } else if (chatContext.page === 'files') {
        // Return to files page with context
        const returnTarget = {
          page: 'files',
          context: chatContext.context,
          resource: '',
          params: {}
        }
        navigationManager.navigateTo(returnTarget)
      }
      
      // Close the overlay
      handleClose()
    } else {
      // Fallback to regular close
      handleClose()
    }
  }

  // YOLO: REMOVED PDF ZOOM HANDLERS - Now handled by DocumentViewer
  // YOLO: REMOVED getFileIcon - Now handled by DocumentViewer

  // ============================================================================
  // FILE RENDERING SECTION
  // ============================================================================
  //
  // REFACTOR TARGET: Extract to FileTypeRenderer plugin system
  // API: const FileTypeRenderer = ({ fileType, content, options }) => { ... }
  //
  // CURRENT ISSUES:
  // - All file type renderers in one component
  // - No plugin architecture for extensibility
  // - Mixed rendering logic with component logic
  //
  // PLUGIN SYSTEM TARGET:
  // const FILE_TYPE_RENDERERS = {
  //   pdf: PDFRenderer,
  //   markdown: MarkdownRenderer,
  //   mermaid: MermaidRenderer,
  //   text: TextRenderer,
  //   image: ImageRenderer,
  //   code: CodeRenderer
  // };
  // ============================================================================

  // YOLO: REMOVED MermaidRenderer component - Now handled by MermaidRenderer plugin

  // YOLO: REMOVED renderFileContent - Now handled by DocumentViewer

  // YOLO: REMOVED renderPDFContent - Now handled by PDFRenderer plugin in DocumentViewer

  // YOLO: REMOVED renderMarkdownContent - Now handled by MarkdownRenderer plugin

  // YOLO: REMOVED renderMermaidContent - Now handled by MermaidRenderer plugin

  // YOLO: REMOVED renderTextContent - Now handled by TextRenderer plugin

  // YOLO: REMOVED renderImageContent - Now handled by ImageRenderer plugin

  // YOLO: REMOVED renderUnsupportedContent - Now handled by UnsupportedRenderer plugin

  // Actions
  const _handleAskAI = async (): Promise<void> => {
    try {
      if (!filePath || !fileName) return;
      const vaultPath = extractContextPath(filePath) || '';

      // Prefer selected ideas; fallback to auto-selected; else just open chat with file
      const selectedIdeaTexts = (intelligenceData.intelligence.fileIntelligence?.key_ideas || [])
        .filter((idea) => idea.user_confirmed || idea.auto_selected)
        .map((idea) => idea.text);

      if (selectedIdeaTexts.length > 0) {
        await askAINavigationService.navigateToChatWithEntities(
          filePath,
          fileName,
          vaultPath,
          selectedIdeaTexts
        );
      } else {
        await askAINavigationService.navigateToChat(
          filePath,
          fileName,
          vaultPath
        );
      }

      askAINavigationService.showNavigationSuccess(fileName);
    } catch (error) {
      console.error('Failed to navigate to Ask AI:', error);
    }
  };

  const _handleExtractText = async (): Promise<void> => {
    try {
      if (!filePath) return;

      let textToCopy = '';

      if (fileTypeInfo?.type === 'pdf' || fileTypeInfo?.type === 'image' || fileTypeInfo?.type === 'powerpoint' || fileTypeInfo?.type === 'excel') {
        if (window.electronAPI?.files?.processFile) {
          const ext = fileTypeInfo.extension;
          try {
            const result = await window.electronAPI.files.processFile(filePath, ext);
            if (result?.success && result?.content?.text) {
              textToCopy = result.content.text as string;
              console.log('✅ Text extracted via processFile:', textToCopy.length, 'characters')
            }
          } catch (e) {
            console.warn('processFile failed, falling back:', e);
          }
        }
      }

      // Fallbacks
      if (!textToCopy && (fileTypeInfo?.type === 'text' || fileTypeInfo?.type === 'code' || fileTypeInfo?.type === 'markdown')) {
        textToCopy = intelligenceData.file.content || '';
        console.log('✅ Text extracted from file content:', textToCopy.length, 'characters')
      }

      if (!textToCopy) {
        // As a last resort, attempt raw read via vault API
        if (window.electronAPI?.vault?.readFile) {
          const readResult = await window.electronAPI.vault.readFile(filePath);
          if (readResult?.success && typeof readResult.content === 'string') {
            textToCopy = readResult.content;
            console.log('✅ Text extracted via processFile:', textToCopy.length, 'characters')
          }
        }
      }

      if (textToCopy) {
        await navigator.clipboard.writeText(textToCopy);
        // Simple user feedback without external deps
        console.log('✅ Text extracted and copied to clipboard. Length:', textToCopy.length);
        alert('Text extracted and copied to clipboard.');
      } else {
        alert('No extractable text found.');
      }
    } catch (error) {
      console.error('Failed to extract and copy text:', error);
        alert('Failed to extract text.');
    }
  };

  // ============================================================================
  // ANNOTATION MANAGEMENT SECTION
  // ============================================================================
  //
  // REFACTOR TARGET: Extract to AnnotationSection component
  // API: const { annotations, submitPrompt, editAnnotation } = useAnnotations(filePath);
  //
  // CURRENT ISSUES:
  // - Annotation logic mixed with main component
  // - Manual state management for annotation editing
  // - No separation between annotation UI and data logic
  //
  // EXTRACTION PLAN:
  // 1. Create AnnotationSection component with all annotation UI
  // 2. Create useAnnotations hook for annotation data management
  // 3. Move all annotation state to AnnotationSection
  // ============================================================================

  // ANNOTATION UI HANDLERS
  // Target: Move to AnnotationSection component
  const _handleExamplePromptClick = (promptText: string): void => {
    console.log('[ANNOTATIONS] 🎯 Example prompt clicked:', promptText);

    // Set the prompt text in the textarea
    // setCustomPromptText(promptText);

    // Focus the textarea
    if (promptTextareaRef.current) {
      promptTextareaRef.current.focus();
      // Move cursor to end of text
      setTimeout(() => {
        if (promptTextareaRef.current) {
          const length = promptText.length;
          promptTextareaRef.current.setSelectionRange(length, length);
        }
      }, 0);
    }
  };

  // YOLO: REMOVED OLD ANNOTATION FUNCTIONS - Now handled by useFileIntelligence hook

  // YOLO: REMOVED OLD ANNOTATION EDITING HANDLERS - Now handled by IntelligenceHub component

  // YOLO: REMOVED OLD USER NOTE FUNCTIONS - Now handled by IntelligenceHub component

  // YOLO: REMOVED renderMarkdownToHTML - Now handled by MarkdownRenderer plugin

  const _handleNextPage = async () => {
    if (false /* pdfDocument && currentPage < totalPages */) {
      const nextPage = 1; // placeholder
      // setCurrentPage(nextPage);
      // await renderPage(pdfDocument, nextPage);
    }
  };

  const _handlePrevPage = async () => {
    if (false /* pdfDocument && currentPage > 1 */) {
      const prevPage = 1; // placeholder
      // setCurrentPage(prevPage);
      // await renderPage(pdfDocument, prevPage);
    }
  };

  // ============================================================================
  // COMPONENT LIFECYCLE & EFFECTS SECTION
  // ============================================================================
  //
  // REFACTOR TARGET: Distribute effects to respective components
  //
  // EXTRACTION PLAN:
  // - Service subscriptions -> Move to respective components
  // - File loading effect -> Move to DocumentViewer component
  // - Annotation loading -> Move to AnnotationSection component
  // ============================================================================

  // Subscribe to kernel events for task progress and intelligence updates
  useEffect(() => {
    let offTask: (() => void) | null = null
    let offIntel: (() => void) | null = null
    try {
      if (window.electronAPI?.events?.on) {
        offTask = window.electronAPI.events.on('task:progress', (payload: any) => {
          // Lightweight state hint; do not alter UI layout
          // Optionally, we could dispatch to a store or log
          console.log('[OVERLAY] task:progress', payload)
        })
        offIntel = window.electronAPI.events.on('intelligence:updated', (payload: any) => {
          // Refresh intelligence data if the updated file matches current path
          if (payload?.filePath && payload.filePath === filePath) {
            console.log('[OVERLAY] intelligence:updated for current file, consider refresh')
            // Future: trigger a lightweight refresh if needed
          }
        })
      }
    } catch (e) {
      console.warn('[OVERLAY] Failed to subscribe to kernel events', e)
    }

    return () => {
      try { if (offTask) offTask() } catch {}
      try { if (offIntel) offIntel() } catch {}
    }
  }, [filePath]);

  // REMOVED: pdfViewerService subscription - Now handled by DocumentViewer

  // MAIN FILE LOADING EFFECT
  // Target: Move to DocumentViewer component
  useEffect(() => {
    console.log('[ANNOTATIONS] 🔍 OVERLAY: useEffect triggered', {
      filePath,
      fileName
    });

    if (filePath && fileName) {
      const typeInfo = detectFileType(fileName);
      setFileTypeInfo(typeInfo);
      console.log('🔄 [OVERLAY] File opened:', filePath, 'Type:', typeInfo.type);
      // REMOVED: loadFileContent(typeInfo) - Now handled by DocumentViewer

      // SmartLabelingInterface is the single source of truth for intelligence
      // Don't clear state here - let SmartLabelingInterface manage it
      console.log('[LABELS] 🔄 OVERLAY: File opened, SmartLabelingInterface will manage intelligence state');

      // Only reset loading states, not the actual intelligence data
      updateIntelligence({ processingStatus: 'idle', error: null });
      console.log('[LABELS] 🔄 OVERLAY: Loading states reset, intelligence state preserved');

      // REMOVED: loadAnnotations call - Now handled automatically by IntelligenceHub component
  // This ensures annotations are loaded when the file path changes
    }
  }, [filePath, fileName]);

  // EARLY RETURN FOR CLOSED STATE
  if (!fileOverlay?.isOpen) return null;

  // ============================================================================
  // MAIN JSX STRUCTURE
  // ============================================================================
  //
  // REFACTOR TARGET: Split into DocumentViewer + IntelligenceHub components
  //
  // CURRENT STRUCTURE:
  // - Left Panel: File viewer with header, content area, and controls
  // - Right Panel: Intelligence hub with labeling interface and annotations
  //
  // TARGET STRUCTURE:
  // <FilePageOverlay>
  //   <DocumentViewer filePath={filePath} content={content} />
  //   <IntelligenceHub intelligenceData={intelligenceData} />
  // </FilePageOverlay>
  // ============================================================================

  return (
    <div id="pdf-viewer-overlay" className="fixed inset-0 bg-gray-900 z-[9999] flex font-['Inter',sans-serif]">

      {/* ========================================================================
          LEFT PANEL - DOCUMENT VIEWER
          ========================================================================

          YOLO: REPLACED WITH DocumentViewer COMPONENT!
          - Extracted file header, content area, and controls
          - Maintains exact same UI/UX
          - All file type rendering handled by DocumentViewer
      */}
      <DocumentViewer
        filePath={filePath}
        fileName={fileName}
        onTextSelection={(selectedText, position) => {
          console.log('[FILE-PAGE-OVERLAY] 📝 Text selected from file:', {
            selectedText: selectedText.substring(0, 50) + '...',
            position,
            filePath
          })
          
          // Route 1: Text selection from file → direct annotation creation
          // Create a new annotation directly in IntelligenceHub
          if (selectedText.trim().length > 3) {
            // Pre-populate the annotation input with the selected text
            const annotationContent = `**Selected Text from File:**\n${selectedText}\n\n**User Note:** `;
            
            // Trigger the annotation creation in IntelligenceHub
            // We'll use a custom event to communicate with IntelligenceHub
            const annotationEvent = new CustomEvent('fileTextSelection', {
              detail: {
                selectedText,
                filePath,
                fileName,
                position,
                annotationContent
              }
            });
            document.dispatchEvent(annotationEvent);
            
            console.log('[FILE-PAGE-OVERLAY] 📝 Dispatched file text selection event for annotation');
          }
        }}
        onContentLoad={(content) => {
          console.log('Content loaded:', content.length, 'characters')
          // Wire to intelligence layer
          updateFileContent(content)
        }}
        onContentExtracted={async (extractedContent, metadata) => {
          console.log('[Flow1] 📝 FilePageOverlay.onContentExtracted called')
          console.log('[Flow1] 📝 extractedContent length:', extractedContent.length, 'characters')
          console.log('[Flow1] 📝 metadata received:', metadata)
          console.log('[Flow1] 📝 filePath:', filePath)
          console.log('[Flow1] 📝 fileName:', fileName)

          // Wire to intelligence layer with plugin metadata
          console.log('[Flow1] 📝 Calling updateFileContentWithMetadata...')
          await updateFileContentWithMetadata(extractedContent, metadata, filePath, fileName)

          console.log('[Flow1] ✅ Plugin metadata integrated into intelligence data structure:', {
            extractionMethod: metadata?.extractionMethod,
            frontmatter: metadata?.frontmatter,
            stats: metadata?.stats,
            pluginMetadata: metadata?.pluginMetadata
          })
        }}
        onClose={handleClose} // YOLO: Pass close handler
      />

      {/* ========================================================================
          RIGHT PANEL - INTELLIGENCE HUB
          ========================================================================

          YOLO: REPLACED WITH IntelligenceHub COMPONENT!
          - Extracted smart labeling interface
          - Extracted annotation display and management
          - Maintains exact same UI/UX
      */}
      <IntelligenceHub
        filePath={filePath}
        fileName={fileName}
        fileContent={intelligenceData.file.content || ''}
        fileIntelligence={intelligenceData.intelligence.fileIntelligence}
        onLabelsChanged={hookHandleLabelsChanged}
        onProcessingComplete={handleProcessingComplete}
        onClose={handleClose}
        onAddChatContent={(chatText) => {
          console.log('[FILE-PAGE-OVERLAY] 📝 Chat content received for annotation:', chatText.substring(0, 50) + '...');
          // This will be handled by IntelligenceHub's handleAddChatContent
        }}
        selectedContextId={chatContext?.context || null} // NEW: Pass context ID for proper vault resolution
        // REMOVED: onAddManualLabel - unused and potentially causing data flow issues
      />
    </div>
  );
};
