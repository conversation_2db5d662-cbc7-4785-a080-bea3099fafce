/**
 * Base API Module
 * Abstract base class for all API modules in the modular registry system
 */

import { APIRegistry, APIEndpoint } from '../../APIRegistry'
import { ValidationSchema, MiddlewareFunction } from '../../types'

export interface ModuleConfig {
  enabled: boolean
  lazy: boolean
  priority: number
  [key: string]: any
}

export interface ModuleDependency {
  name: string
  version?: string
  optional?: boolean
}

export interface APIModuleInterface {
  readonly name: string
  readonly version: string
  readonly description: string
  readonly dependencies: ModuleDependency[]

  initialize(registry: APIRegistry, config: ModuleConfig): Promise<void>
  registerEndpoints(): Promise<void>
  cleanup(): Promise<void>

  isInitialized(): boolean
  getHealth(): ModuleHealth
}

export interface ModuleHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  lastCheck: Date
  errors: string[]
  metrics: {
    endpointCount: number
    averageResponseTime: number
    errorRate: number
  }
}

export abstract class BaseAPIModule implements APIModuleInterface {
  abstract readonly name: string
  abstract readonly version: string
  abstract readonly description: string
  abstract readonly dependencies: ModuleDependency[]

  protected registry!: APIRegistry
  protected config!: ModuleConfig
  protected initialized = false
  protected endpoints: Map<string, APIEndpoint> = new Map()
  protected dependencyInstances: Map<string, any> = new Map()

  /**
   * Initialize the module with registry and configuration
   */
  async initialize(registry: APIRegistry, config: ModuleConfig): Promise<void> {
    console.log(`[MODULE] *** BaseAPIModule.initialize() START for ${this.name} ***`)
    this.registry = registry
    this.config = config

    try {
      console.log(`[MODULE] *** COMPILATION TEST *** Initializing module ${this.name} v${this.version} *** COMPILATION TEST ***`)

      // Resolve dependencies
      console.log(`[MODULE] About to resolve dependencies for module ${this.name}`)
      await this.resolveDependencies()
      console.log(`[MODULE] Dependencies resolved successfully for module ${this.name}`)

      // Module-specific initialization
      console.log(`[MODULE] About to call onInitialize() for module ${this.name}`)
      console.log(`[MODULE] onInitialize method type:`, typeof this.onInitialize)
      console.log(`[MODULE] onInitialize method:`, this.onInitialize.toString().substring(0, 200))
      try {
        await this.onInitialize()
        console.log(`[MODULE] onInitialize() completed successfully for module ${this.name}`)
      } catch (error) {
        console.error(`[MODULE] onInitialize() failed for module ${this.name}:`, error)
        throw error
      }

      // Register endpoints
      await this.registerEndpoints()

      this.initialized = true
      console.log(`[MODULE] Module ${this.name} initialized successfully`)
    } catch (error) {
      console.error(`[MODULE] Failed to initialize module ${this.name}:`, error)
      throw error
    }
  }

  /**
   * Register all endpoints for this module
   */
  abstract registerEndpoints(): Promise<void>

  /**
   * Module-specific initialization logic
   */
  protected async onInitialize(): Promise<void> {
    // Override in subclasses if needed
  }

  /**
   * Clean up module resources
   */
  async cleanup(): Promise<void> {
    try {
      console.log(`[MODULE] Cleaning up module ${this.name}`)

      // Module-specific cleanup
      await this.onCleanup()

      // Clear endpoints
      this.endpoints.clear()

      // Clear dependencies
      this.dependencyInstances.clear()

      this.initialized = false
      console.log(`[MODULE] Module ${this.name} cleaned up successfully`)
    } catch (error) {
      console.error(`[MODULE] Failed to cleanup module ${this.name}:`, error)
      throw error
    }
  }

  /**
   * Module-specific cleanup logic
   */
  protected async onCleanup(): Promise<void> {
    // Override in subclasses if needed
  }

  /**
   * Check if module is initialized
   */
  isInitialized(): boolean {
    return this.initialized
  }

  /**
   * Get module health status
   */
  getHealth(): ModuleHealth {
    const endpointMetrics = this.getEndpointMetrics()

    return {
      status: this.initialized ? 'healthy' : 'unhealthy',
      lastCheck: new Date(),
      errors: [],
      metrics: {
        endpointCount: this.endpoints.size,
        averageResponseTime: endpointMetrics.averageResponseTime,
        errorRate: endpointMetrics.errorRate
      }
    }
  }

  /**
   * Resolve module dependencies
   */
  protected async resolveDependencies(): Promise<void> {
    for (const dep of this.dependencies) {
      try {
        // Get the dependency instance from the global module registry
        const instance = await this.resolveDependencyFromRegistry(dep.name)
        if (instance) {
          this.dependencyInstances.set(dep.name, instance)
          console.log(`[MODULE] Resolved dependency: ${dep.name}`)
        } else if (!dep.optional) {
          throw new Error(`Required dependency not available: ${dep.name}`)
        }
      } catch (error) {
        if (!dep.optional) {
          throw new Error(`Failed to resolve required dependency: ${dep.name}`)
        }
        console.warn(`[MODULE] Optional dependency not available: ${dep.name}`)
      }
    }
  }

  /**
   * Resolve dependency from the global module registry or create shared instances
   */
  private async resolveDependencyFromRegistry(depName: string): Promise<any> {
    // Try to get from global module registry first
    const moduleRegistry = (global as any).moduleRegistry
    if (moduleRegistry && moduleRegistry.getModule) {
      const module = moduleRegistry.getModule(depName)
      if (module) {
        return module
      }
    }

    // For database dependency, create or get shared DatabaseManager instance
    if (depName === 'database') {
      if (!(global as any).sharedDatabaseManager) {
        const { DatabaseManager } = await import('../../../database')
        ;(global as any).sharedDatabaseManager = new DatabaseManager()
        console.log(`[MODULE] Created shared DatabaseManager instance`)
      }
      return (global as any).sharedDatabaseManager
    }

    // For other dependencies, try to resolve from known patterns
    return null
  }

  /**
   * Get a resolved dependency instance
   */
  protected getDependency<T = any>(name: string): T {
    const instance = this.dependencyInstances.get(name)
    if (!instance) {
      throw new Error(`Dependency not resolved: ${name}`)
    }
    return instance as T
  }

  /**
   * Register an endpoint with the API registry
   */
  protected registerEndpoint(
    category: string,
    name: string,
    handler: Function,
    options?: {
      validator?: Function
      validationSchema?: ValidationSchema
      middleware?: MiddlewareFunction[]
      description?: string
      requiresAuth?: boolean
      requiredPermission?: string
      rateLimit?: { maxRequests: number; windowMs: number }
    }
  ): void {
    const endpointKey = `${category}:${name}`

    // Store endpoint reference for health monitoring
    this.endpoints.set(endpointKey, {
      handler,
      validator: options?.validator,
      validationSchema: options?.validationSchema,
      middleware: options?.middleware || [],
      description: options?.description,
      requiresAuth: options?.requiresAuth,
      requiredPermission: options?.requiredPermission,
      rateLimit: options?.rateLimit
    })

    // Register with the main registry
    this.registry.registerEndpoint(category, name, handler, options)

    console.log(`[MODULE] Registered endpoint: ${endpointKey}`)
  }

  /**
   * Shared validation utility
   */
  protected validateInput(value: any, type: string, maxLength?: number): boolean {
    if (value === null || value === undefined) {
      return false
    }

    switch (type) {
      case 'string':
        if (typeof value !== 'string') return false
        if (maxLength && value.length > maxLength) return false
        return true
      case 'number':
        return typeof value === 'number' && !isNaN(value)
      case 'boolean':
        return typeof value === 'boolean'
      case 'object':
        return typeof value === 'object' && value !== null
      case 'array':
        return Array.isArray(value)
      default:
        return false
    }
  }

  /**
   * Create a validation function from schema
   */
  protected createValidator(schema: ValidationSchema): Function {
    return (value: any) => {
      // Basic schema validation implementation
      // ValidationSchema is now a record of field -> ValidationRule
      // This is a simplified implementation
      for (const [field, rule] of Object.entries(schema)) {
        if (Array.isArray(rule)) {
          // Multiple rules for this field
          for (const r of rule) {
            if (!this.validateField(value[field], r)) {
              return false
            }
          }
        } else {
          // Single rule for this field
          if (!this.validateField(value[field], rule)) {
            return false
          }
        }
      }
      return true
    }
  }

  /**
   * Validate a single field against a rule
   */
  private validateField(value: any, rule: any): boolean {
    // Simplified validation - in real implementation would be more comprehensive
    if (rule.type === 'required' && (value === undefined || value === null)) {
      return false
    }
    return true
  }

  /**
   * Add middleware to the module
   */
  protected addMiddleware(middleware: MiddlewareFunction[]): void {
    // Store middleware for use in endpoint registration
    // Implementation depends on middleware system design
  }

  /**
   * Get endpoint performance metrics
   */
  protected getEndpointMetrics(): { averageResponseTime: number; errorRate: number } {
    // In a real implementation, this would collect actual metrics
    // For now, return placeholder values
    return {
      averageResponseTime: 0,
      errorRate: 0
    }
  }

  /**
   * Create a standardized error response
   */
  protected createErrorResponse(error: Error, context?: any): any {
    return {
      success: false,
      error: error.message,
      context: context || {},
      timestamp: new Date().toISOString(),
      module: this.name
    }
  }

  /**
   * Create a standardized success response
   */
  protected createSuccessResponse(data?: any, message?: string): any {
    return {
      success: true,
      data: data || {},
      message: message || 'Operation completed successfully',
      timestamp: new Date().toISOString(),
      module: this.name
    }
  }

  /**
   * Log module activity
   */
  protected log(level: 'info' | 'warn' | 'error' | 'debug', message: string, ...args: any[]): void {
    const prefix = `[MODULE:${this.name}]`
    switch (level) {
      case 'info':
        console.log(prefix, message, ...args)
        break
      case 'warn':
        console.warn(prefix, message, ...args)
        break
      case 'error':
        console.error(prefix, message, ...args)
        break
      case 'debug':
        console.debug(prefix, message, ...args)
        break
    }
  }
}
