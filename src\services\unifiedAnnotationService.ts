/**
 * Unified Annotation Storage Service
 * REPLACES: Legacy annotationStorageService with isVirtualPath system
 * IMPLEMENTS: Unified path resolution using PathResolver and VaultContextService
 */

import { SmartAnnotationNote, FileIntelligence } from '../types/fileIntelligenceTypes'
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'
import { vaultContextService } from './vaultContextService'

export class UnifiedAnnotationService {
  
  /**
   * Load all annotations for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Array of annotations
   */
  async loadAnnotations(filePath: string, currentContextId?: string | null): Promise<SmartAnnotationNote[]> {
    try {
      // Use unified path resolution - NO MORE isVirtualPath
      const resolution = await vaultContextService.resolvePath(filePath, {
        context: currentContextId || undefined
      })
      
      console.log('[UNIFIED-ANNOTATIONS] 📖 Loading annotations with unified path resolution:', { 
        originalPath: filePath,
        resolvedType: resolution.type,
        vault: resolution.vault,
        context: resolution.context,
        finalPath: resolution.path
      })

      const result = await intelligenceClient.read(resolution.path, resolution.vault)

      if (result && result.success !== false && result.data) {
        const intelligence = result.data as FileIntelligence

        console.log('[UNIFIED-ANNOTATIONS] 🔍 Intelligence data structure:', {
          hasKeyIdeas: !!intelligence.key_ideas,
          keyIdeasCount: intelligence.key_ideas?.length || 0,
          hasSmartAnnotations: !!intelligence.smart_annotations,
          smartAnnotationsCount: intelligence.smart_annotations?.length || 0,
          pathType: resolution.type
        })

        if (intelligence.smart_annotations && Array.isArray(intelligence.smart_annotations)) {
          return intelligence.smart_annotations
        }
      }

      return []
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to load annotations:', error)
      return []
    }
  }

  /**
   * Save a single annotation for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotation - Single annotation to save
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async saveAnnotation(
    filePath: string,
    annotation: SmartAnnotationNote,
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      console.log('[UNIFIED-ANNOTATIONS] 💾 saveAnnotation called with:', {
        filePath,
        annotationId: annotation.id,
        currentContextId
      });
      
      // Load existing annotations and add the new one
      const existingAnnotations = await this.loadAnnotations(filePath, currentContextId)
      console.log('[UNIFIED-ANNOTATIONS] 📖 Loaded existing annotations:', existingAnnotations.length);
      
      const updatedAnnotations = [...existingAnnotations, annotation]
      console.log('[UNIFIED-ANNOTATIONS] 📝 Updated annotations count:', updatedAnnotations.length);
      
      // Save updated annotations
      const result = await this.saveAnnotations(filePath, updatedAnnotations, currentContextId)
      console.log('[UNIFIED-ANNOTATIONS] 💾 saveAnnotations result:', result);
      
      return result
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to save annotation:', error)
      return false
    }
  }

  /**
   * Save annotations for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotations - Array of annotations to save
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async saveAnnotations(
    filePath: string, 
    annotations: SmartAnnotationNote[], 
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      // Use unified path resolution - NO MORE isVirtualPath
      const resolution = await vaultContextService.resolvePath(filePath, {
        context: currentContextId || undefined
      })
      
      console.log('[UNIFIED-ANNOTATIONS] 💾 Saving annotations with unified path resolution:', { 
        originalPath: filePath,
        resolvedType: resolution.type,
        vault: resolution.vault,
        context: resolution.context,
        finalPath: resolution.path,
        annotationCount: annotations.length
      })

      // Load existing intelligence data
      let existingIntelligence: FileIntelligence = {
        file_path: resolution.path,
        key_ideas: [],
        weighted_entities: [],
        human_connections: [],
        processing_confidence: 0,
        analysis_metadata: {
          processing_time_ms: 0,
          model_used: 'unified-annotation-service',
          timestamp: new Date().toISOString()
        },
        smart_annotations: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      try {
        const existingResult = await intelligenceClient.read(resolution.path, resolution.vault)
        if (existingResult && existingResult.success !== false && existingResult.data) {
          existingIntelligence = existingResult.data as FileIntelligence
        }
      } catch (error) {
        console.log('[UNIFIED-ANNOTATIONS] ℹ️ No existing intelligence data, creating new')
      }

      // CRITICAL FIX: The system has conflicting expectations for file_path format:
      // 1. Security validation expects RELATIVE paths (no absolute paths allowed)
      // 2. Corruption detection expects the NORMALIZED version of the filePath parameter
      //
      // The solution: Store the vault-relative path (relative to the vault context)
      // Example: "documents/filename.ext" instead of just "filename.ext" or full absolute path

      let vaultRelativeFilePath: string

      if (resolution.path.includes('.intelligence/context-notes/') || resolution.path.includes('\\.intelligence\\context-notes\\')) {
        // This is an annotation file - we need to reconstruct the original file path
        // Extract the original filename from the annotation filename
        const annotationFilename = resolution.path.split(/[\\\/]/).pop() || ''
        const originalFilename = annotationFilename.replace('.json', '')

        // Store as vault-relative path: "documents/filename.ext"
        vaultRelativeFilePath = `documents/${originalFilename}`

        console.log('[UNIFIED-ANNOTATIONS] 🔍 ANNOTATION PATH RECONSTRUCTION:', {
          annotationPath: resolution.path,
          annotationFilename,
          originalFilename,
          vaultRelativeFilePath
        })
      } else {
        // This is a regular file - extract the vault-relative path
        // From: C:\...\vault\context\documents\file.ext
        // To: documents/file.ext
        const vaultPath = resolution.vault
        if (resolution.path.startsWith(vaultPath)) {
          vaultRelativeFilePath = resolution.path.substring(vaultPath.length + 1).replace(/\\/g, '/')
        } else {
          // Fallback: just use the filename
          vaultRelativeFilePath = resolution.path.split(/[\\\/]/).pop() || ''
        }

        console.log('[UNIFIED-ANNOTATIONS] 🔍 REGULAR FILE PATH CONVERSION:', {
          originalPath: resolution.path,
          vaultPath: resolution.vault,
          vaultRelativeFilePath
        })
      }

      // Update with new annotations - ensure required fields are present
      const updatedIntelligence: FileIntelligence = {
        // Default values for required fields
        key_ideas: [],
        weighted_entities: [],
        human_connections: [],
        processing_confidence: 0,
        analysis_metadata: {
          processing_time_ms: 0,
          model_used: 'annotation-service',
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString(),
        // Spread existing intelligence data (if any) to preserve existing values
        ...existingIntelligence,
        // CRITICAL: Always override file_path and annotations AFTER spread
        // Use vault-relative path to satisfy both security validation and corruption detection
        file_path: vaultRelativeFilePath, // Use vault-relative path (e.g., "documents/file.ext")
        smart_annotations: annotations,
        updated_at: new Date().toISOString()
      }

      // Save updated intelligence
      console.log('[UNIFIED-ANNOTATIONS] 💾 About to save to intelligence client:', {
        path: resolution.path,
        vault: resolution.vault,
        annotationCount: annotations.length
      });
      console.log('[UNIFIED-ANNOTATIONS] 🔍 DEBUG: updatedIntelligence structure:', JSON.stringify(updatedIntelligence, null, 2))
      console.log('[UNIFIED-ANNOTATIONS] 🔍 DEBUG: Data being sent to backend:', JSON.stringify({ json: updatedIntelligence }, null, 2))

      const saveResult = await intelligenceClient.write(
        resolution.path,
        resolution.vault,
        { json: updatedIntelligence }
      )

      if (saveResult && saveResult.success !== false) {
        console.log('[UNIFIED-ANNOTATIONS] ✅ Annotations saved successfully')
        return true
      } else {
        console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to save annotations:', saveResult)
        return false
      }
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to save annotations:', error)
      // CRITICAL: Don't crash the app, return false instead
      return false
    }
  }

  /**
   * Add a single annotation using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotation - The annotation to add
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async addAnnotation(
    filePath: string, 
    annotation: SmartAnnotationNote, 
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      // Load existing annotations
      const existingAnnotations = await this.loadAnnotations(filePath, currentContextId)
      
      // Add new annotation
      const updatedAnnotations = [...existingAnnotations, annotation]
      
      // Save updated annotations
      return await this.saveAnnotations(filePath, updatedAnnotations, currentContextId)
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to add annotation:', error)
      return false
    }
  }



  /**
   * Update an existing annotation using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotationId - The ID of the annotation to update
   * @param updates - Partial annotation updates
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async updateAnnotation(
    filePath: string,
    annotationId: string,
    updates: Partial<SmartAnnotationNote>,
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      // Load existing annotations
      const existingAnnotations = await this.loadAnnotations(filePath, currentContextId)

      // Find and update the annotation
      const updatedAnnotations = existingAnnotations.map(ann =>
        ann.id === annotationId ? { ...ann, ...updates } : ann
      )

      // Save updated annotations
      return await this.saveAnnotations(filePath, updatedAnnotations, currentContextId)
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to update annotation:', error)
      return false
    }
  }

  /**
   * Generate an annotation with AI response (placeholder implementation)
   * @param filePath - The file path
   * @param prompt - User prompt
   * @param content - File content
   * @returns Generated annotation or null
   */
  async generateAnnotation(
    filePath: string,
    prompt: string,
    content: string
  ): Promise<SmartAnnotationNote | null> {
    try {
      // Create a basic annotation - AI generation would be implemented here
              const annotation: SmartAnnotationNote = {
          id: `annotation-${Date.now()}`,
          type: 'user',
          content: prompt,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          note_number: 1,
          has_ai_response: false,
          labels_snapshot: []
        }

      return annotation
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to generate annotation:', error)
      return null
    }
  }

  /**
   * Delete an annotation using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param annotationId - The ID of the annotation to delete
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async deleteAnnotation(
    filePath: string, 
    annotationId: string, 
    currentContextId?: string | null
  ): Promise<boolean> {
    try {
      // Load existing annotations
      const existingAnnotations = await this.loadAnnotations(filePath, currentContextId)
      
      // Filter out the annotation to delete
      const updatedAnnotations = existingAnnotations.filter(ann => ann.id !== annotationId)
      
      if (updatedAnnotations.length === existingAnnotations.length) {
        console.error('[UNIFIED-ANNOTATIONS] ❌ Annotation not found for deletion:', annotationId)
        return false
      }
      
      // Save updated annotations
      return await this.saveAnnotations(filePath, updatedAnnotations, currentContextId)
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to delete annotation:', error)
      return false
    }
  }

  /**
   * Clear all annotations for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async clearAnnotations(filePath: string, currentContextId?: string | null): Promise<boolean> {
    try {
      return await this.saveAnnotations(filePath, [], currentContextId)
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to clear annotations:', error)
      return false
    }
  }

  /**
   * Update key ideas for a file using unified path resolution
   * @param filePath - The file path (any type: file, annotation, vault-relative)
   * @param keyIdeas - Array of key ideas to save
   * @param currentContextId - The currently selected context ID (optional)
   * @returns Success status
   */
  async updateKeyIdeas(filePath: string, keyIdeas: any[], currentContextId?: string | null): Promise<boolean> {
    try {
      // Use unified path resolution
      const resolution = await vaultContextService.resolvePath(filePath, {
        context: currentContextId || undefined
      })

      console.log('[UNIFIED-ANNOTATIONS] 🏷️ Updating key ideas with unified path resolution:', {
        originalPath: filePath,
        resolvedType: resolution.type,
        vault: resolution.vault,
        context: resolution.context,
        finalPath: resolution.path,
        keyIdeasCount: keyIdeas.length
      })

      // Load existing intelligence data
      let existingIntelligence: FileIntelligence = {
        file_path: resolution.path,
        key_ideas: [],
        weighted_entities: [],
        human_connections: [],
        processing_confidence: 0,
        analysis_metadata: {
          processing_time_ms: 0,
          model_used: 'unified-annotation-service',
          timestamp: new Date().toISOString()
        },
        smart_annotations: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      try {
        const existingResult = await intelligenceClient.read(resolution.path, resolution.vault)
        if (existingResult && existingResult.success !== false && existingResult.data) {
          existingIntelligence = existingResult.data as FileIntelligence
        }
      } catch (error) {
        console.log('[UNIFIED-ANNOTATIONS] ℹ️ No existing intelligence data for key ideas, creating new')
      }

      // Update with new key ideas
      const updatedIntelligence: FileIntelligence = {
        ...existingIntelligence,
        key_ideas: keyIdeas,
        updated_at: new Date().toISOString()
      }

      // Save updated intelligence
      const saveResult = await intelligenceClient.write(
        resolution.path,
        resolution.vault,
        { json: updatedIntelligence }
      )

      if (saveResult && saveResult.success !== false) {
        console.log('[UNIFIED-ANNOTATIONS] ✅ Key ideas saved successfully')
        return true
      } else {
        console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to save key ideas:', saveResult)
        return false
      }
    } catch (error) {
      console.error('[UNIFIED-ANNOTATIONS] ❌ Failed to update key ideas:', error)
      return false
    }
  }
}

// Export singleton instance
export const unifiedAnnotationService = new UnifiedAnnotationService()
