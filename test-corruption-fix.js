/**
 * Test script to verify the corruption detection fix
 * This script simulates the intelligence operations to test if the new
 * migration-aware corruption detection is working correctly.
 */

const { ipc<PERSON><PERSON><PERSON> } = require('electron');

async function testCorruptionDetectionFix() {
  console.log('🧪 Testing corruption detection fix...');
  
  const testFilePath = 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started\\documents\\22年China SEO Report_February for Vendor.pptx';
  const testVaultPath = 'C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3\\personal-vault\\getting-started';
  
  try {
    // Test 1: Try to read existing intelligence data
    console.log('📖 Test 1: Reading existing intelligence data...');
    const readResult = await ipcRenderer.invoke('intelligence:read', testFilePath, testVaultPath);
    console.log('📖 Read result:', readResult);
    
    // Test 2: Try to write new intelligence data with vault-relative path
    console.log('✍️ Test 2: Writing intelligence data with vault-relative path...');
    const testData = {
      json: {
        file_path: 'documents/22年China SEO Report_February for Vendor.pptx', // Vault-relative path
        key_ideas: [
          {
            id: 'test_idea_1',
            text: 'Test idea for corruption fix',
            relevance_score: 90,
            intent_types: ['topic'],
            weight: 0.9,
            auto_selected: true
          }
        ],
        weighted_entities: [],
        human_connections: [],
        processing_confidence: 85,
        analysis_metadata: {
          processing_time_ms: 100,
          model_used: 'test-model',
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };
    
    const writeResult = await ipcRenderer.invoke('intelligence:write', testFilePath, testVaultPath, testData);
    console.log('✍️ Write result:', writeResult);
    
    // Test 3: Try to read the data again to see if corruption detection passes
    console.log('📖 Test 3: Reading data again after write...');
    const readResult2 = await ipcRenderer.invoke('intelligence:read', testFilePath, testVaultPath);
    console.log('📖 Read result 2:', readResult2);
    
    // Test 4: Check if the new corruption detection logic is being used
    if (readResult2 && readResult2.success && readResult2.data) {
      console.log('✅ SUCCESS: Corruption detection fix is working!');
      console.log('📊 Data retrieved successfully:', {
        file_path: readResult2.data.file_path,
        key_ideas_count: readResult2.data.key_ideas?.length || 0,
        has_analysis_metadata: !!readResult2.data.analysis_metadata
      });
    } else {
      console.log('❌ FAILED: Corruption detection is still blocking data access');
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(testCorruptionDetectionFix, 2000); // Wait 2 seconds for app to fully load
});

module.exports = { testCorruptionDetectionFix };
